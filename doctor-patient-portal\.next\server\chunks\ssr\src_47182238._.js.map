{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/logo.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Stethoscope } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n  className?: string;\n  clickable?: boolean;\n  variant?: 'default' | 'compact';\n}\n\nexport default function Logo({ \n  size = 'md', \n  showText = true, \n  className,\n  clickable = true,\n  variant = 'default'\n}: LogoProps) {\n  const router = useRouter();\n\n  const sizeClasses = {\n    sm: {\n      icon: 'w-4 h-4',\n      container: 'p-1.5',\n      text: 'text-sm',\n      subtext: 'text-xs'\n    },\n    md: {\n      icon: 'w-6 h-6',\n      container: 'p-2',\n      text: 'text-lg',\n      subtext: 'text-xs'\n    },\n    lg: {\n      icon: 'w-8 h-8',\n      container: 'p-3',\n      text: 'text-xl',\n      subtext: 'text-sm'\n    }\n  };\n\n  const handleClick = () => {\n    if (clickable) {\n      router.push('/');\n    }\n  };\n\n  const logoContent = (\n    <div className={cn(\n      'flex items-center space-x-2',\n      clickable && 'cursor-pointer group',\n      className\n    )}>\n      <motion.div \n        className={cn(\n          'bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg shadow-sm',\n          sizeClasses[size].container,\n          clickable && 'group-hover:shadow-md transition-shadow duration-200'\n        )}\n        whileHover={clickable ? { scale: 1.05 } : {}}\n        whileTap={clickable ? { scale: 0.95 } : {}}\n      >\n        <Stethoscope className={cn('text-white', sizeClasses[size].icon)} />\n      </motion.div>\n      \n      {showText && (\n        <div className={cn(\n          'flex flex-col',\n          variant === 'compact' && 'hidden sm:flex'\n        )}>\n          <h1 className={cn(\n            'font-bold text-gray-900 leading-tight',\n            sizeClasses[size].text,\n            clickable && 'group-hover:text-blue-600 transition-colors duration-200'\n          )}>\n            HealthCare\n          </h1>\n          {variant === 'default' && (\n            <p className={cn(\n              'text-gray-500 leading-tight',\n              sizeClasses[size].subtext\n            )}>\n              Portal\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n\n  if (clickable) {\n    return (\n      <motion.div\n        onClick={handleClick}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className=\"inline-block\"\n      >\n        {logoContent}\n      </motion.div>\n    );\n  }\n\n  return logoContent;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS,KAAK,EAC3B,OAAO,IAAI,EACX,WAAW,IAAI,EACf,SAAS,EACT,YAAY,IAAI,EAChB,UAAU,SAAS,EACT;IACV,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc;QAClB,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,+BACA,aAAa,wBACb;;0BAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WAAW,CAAC,KAAK,CAAC,SAAS,EAC3B,aAAa;gBAEf,YAAY,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAC3C,UAAU,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;0BAEzC,cAAA,8OAAC,gNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,WAAW,CAAC,KAAK,CAAC,IAAI;;;;;;;;;;;YAGhE,0BACC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iBACA,YAAY,aAAa;;kCAEzB,8OAAC;wBAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,yCACA,WAAW,CAAC,KAAK,CAAC,IAAI,EACtB,aAAa;kCACZ;;;;;;oBAGF,YAAY,2BACX,8OAAC;wBAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,+BACA,WAAW,CAAC,KAAK,CAAC,OAAO;kCACxB;;;;;;;;;;;;;;;;;;IASb,IAAI,WAAW;QACb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;YACT,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,WAAU;sBAET;;;;;;IAGP;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Home,\n  Calendar,\n  Users,\n  User,\n  LogOut,\n  Menu,\n  X,\n  ClipboardList,\n  Search,\n  Bell,\n} from 'lucide-react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { toast } from 'sonner';\nimport Logo from '@/components/ui/logo';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport default function Sidebar({ className }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const isDoctor = user?.role === 'doctor';\n\n  const doctorNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/doctor/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Appointments',\n      href: '/doctor/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/doctor/profile',\n      icon: User,\n    },\n  ];\n\n  const patientNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/patient/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Find Doctors',\n      href: '/doctors',\n      icon: Search,\n    },\n    {\n      title: 'My Appointments',\n      href: '/patient/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/patient/profile',\n      icon: User,\n    },\n  ];\n\n  const navItems = isDoctor ? doctorNavItems : patientNavItems;\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      await logout();\n      toast.success('Logged out successfully');\n    } catch (error) {\n      toast.error('Failed to logout');\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';\n    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';\n    return `${first}${last}`.toUpperCase();\n  };\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n    closed: {\n      x: '-100%',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n  };\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        variants={sidebarVariants}\n        animate={sidebarOpen ? 'open' : 'closed'}\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-xl lg:relative lg:translate-x-0 lg:shadow-none',\n          'flex flex-col',\n          className\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <Logo size=\"md\" variant=\"default\" />\n\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleSidebar}\n              className=\"lg:hidden hover:bg-gray-100 transition-colors\"\n            >\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 p-4 space-y-1 overflow-y-auto\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              const Icon = item.icon;\n\n              return (\n                <motion.div\n                  key={item.href}\n                  whileHover={{ x: 2 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"relative\"\n                >\n                  <Button\n                    variant={isActive ? 'default' : 'ghost'}\n                    size=\"default\"\n                    className={cn(\n                      'w-full justify-start text-left h-11 px-3 rounded-lg transition-all duration-200',\n                      isActive\n                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm hover:from-blue-700 hover:to-blue-800'\n                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                    )}\n                    onClick={() => {\n                      router.push(item.href);\n                      if (typeof window !== 'undefined' && window.innerWidth < 1024) {\n                        toggleSidebar();\n                      }\n                    }}\n                  >\n                    <Icon className={cn(\n                      'w-5 h-5 mr-3 flex-shrink-0',\n                      isActive ? 'text-white' : 'text-gray-500'\n                    )} />\n                    <span className=\"truncate\">{item.title}</span>\n                  </Button>\n\n                  {isActive && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className=\"absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-r-full\"\n                      initial={false}\n                      transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                    />\n                  )}\n                </motion.div>\n              );\n            })}\n          </nav>\n\n          {/* User Profile */}\n          <div className=\"p-4 border-t border-gray-200 bg-gray-50/50\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start p-3 h-auto rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200\"\n                >\n                  <div className=\"flex items-center space-x-3 w-full\">\n                    <Avatar className=\"w-10 h-10 ring-2 ring-blue-100\">\n                      <AvatarImage\n                        src={user?.profileImage}\n                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}\n                      />\n                      <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold\">\n                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}\n                      </AvatarFallback>\n                    </Avatar>\n\n                    <div className=\"flex-1 text-left min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}\n                      </p>\n                      <p className=\"text-xs text-gray-500 capitalize\">\n                        {user?.role || 'User'}\n                      </p>\n                    </div>\n\n                    <div className=\"text-gray-400\">\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </div>\n                  </div>\n                </Button>\n              </DropdownMenuTrigger>\n              \n              <DropdownMenuContent align=\"end\" className=\"w-56 shadow-lg border-0 bg-white\">\n                <DropdownMenuItem\n                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}\n                  className=\"cursor-pointer hover:bg-gray-50 transition-colors\"\n                >\n                  <User className=\"mr-2 h-4 w-4 text-gray-500\" />\n                  <span>Profile</span>\n                </DropdownMenuItem>\n\n                <DropdownMenuSeparator className=\"bg-gray-100\" />\n\n                <DropdownMenuItem\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"cursor-pointer text-red-600 hover:bg-red-50 hover:text-red-700 focus:text-red-700 transition-colors\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </motion.aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AA9BA;;;;;;;;;;;;;;AAoCe,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,MAAM,SAAS;IAEhC,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,WAAW,WAAW,iBAAiB;IAE7C,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,MAAM,QAAQ,aAAa,UAAU,MAAM,GAAG,IAAI,UAAU,MAAM,CAAC,KAAK;QACxE,MAAM,OAAO,YAAY,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,CAAC,KAAK;QACpE,OAAO,GAAG,QAAQ,MAAM,CAAC,WAAW;IACtC;IAEA,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE;;0BAEE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA,iBACA;0BAGF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,UAAI;oCAAC,MAAK;oCAAK,SAAQ;;;;;;8CAExB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,YAAY;4CAChC,MAAK;4CACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA,WACI,0GACA;4CAEN,SAAS;gDACP,OAAO,IAAI,CAAC,KAAK,IAAI;gDACrB,IAAI,gBAAkB,eAAe,OAAO,UAAU,GAAG,MAAM;;gDAE/D;4CACF;;8DAEA,8OAAC;oDAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,8BACA,WAAW,eAAe;;;;;;8DAE5B,8OAAC;oDAAK,WAAU;8DAAY,KAAK,KAAK;;;;;;;;;;;;wCAGvC,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAjCzD,KAAK,IAAI;;;;;4BAsCpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEACV,KAAK,MAAM;gEACX,KAAK,GAAG,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM,YAAY,IAAI;;;;;;0EAE7D,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,QAAQ,YAAY,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ,IAAI;;;;;;;;;;;;kEAIjE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEACV,WAAW,SAAS;oEAAI,MAAM,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;oEAAO;oEAAE,MAAM,YAAY;;;;;;;0EAEvG,8OAAC;gEAAE,WAAU;0EACV,MAAM,QAAQ;;;;;;;;;;;;kEAInB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/E,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,OAAO,IAAI,CAAC,WAAW,oBAAoB;gDAC1D,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC,4IAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;0DAEjC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAM,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Menu, Bell, Search } from 'lucide-react';\nimport { useUIStore, useNotificationStore } from '@/lib/store';\n\ninterface HeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nexport default function Header({ title, subtitle }: HeaderProps) {\n  const { toggleSidebar } = useUIStore();\n  const { notifications, unreadCount, markAsRead } = useNotificationStore();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleNotificationClick = (index: number) => {\n    markAsRead(index);\n  };\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-4 py-3 lg:px-6\">\n      <div className=\"flex items-center justify-between\">\n        {/* Left side */}\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleSidebar}\n            className=\"lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Search - Hidden on mobile */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                <Bell className=\"w-5 h-5\" />\n                {unreadCount > 0 && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className=\"absolute -top-1 -right-1\"\n                  >\n                    <Badge \n                      variant=\"destructive\" \n                      className=\"h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                    >\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </Badge>\n                  </motion.div>\n                )}\n              </Button>\n            </DropdownMenuTrigger>\n            \n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <h3 className=\"font-semibold text-gray-900\">Notifications</h3>\n                {unreadCount > 0 && (\n                  <p className=\"text-sm text-gray-600\">\n                    You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"max-h-96 overflow-y-auto\">\n                {notifications.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    <Bell className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                    <p className=\"text-sm\">No notifications yet</p>\n                  </div>\n                ) : (\n                  notifications.slice(0, 10).map((notification, index) => (\n                    <DropdownMenuItem\n                      key={index}\n                      className=\"p-3 cursor-pointer hover:bg-gray-50\"\n                      onClick={() => handleNotificationClick(index)}\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(notification.timestamp).toLocaleString()}\n                        </p>\n                      </div>\n                      {index < unreadCount && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full ml-2\" />\n                      )}\n                    </DropdownMenuItem>\n                  ))\n                )}\n              </div>\n\n              {notifications.length > 10 && (\n                <div className=\"p-3 border-t border-gray-200 text-center\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-blue-600\">\n                    View all notifications\n                  </Button>\n                </div>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Mobile search button */}\n          <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n            <Search className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AACA;AAbA;;;;;;;;;AAoBe,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,0BAA0B,CAAC;QAC/B,WAAW;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;gCACpD,0BACC,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAM5C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;8CAOpC,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;gDAC3C,cAAc,mBACb,8OAAC;oDAAE,WAAU;;wDAAwB;wDACzB;wDAAY;wDAAqB,gBAAgB,IAAI,MAAM;;;;;;;;;;;;;sDAK3E,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;uDAGzB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc,sBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;oDAEf,WAAU;oDACV,SAAS,IAAM,wBAAwB;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;;;;;;;wDAGnD,QAAQ,6BACP,8OAAC;4DAAI,WAAU;;;;;;;mDAbZ;;;;;;;;;;wCAoBZ,cAAc,MAAM,GAAG,oBACtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCASpE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;sCAC1C,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle?: string;\n}\n\nexport default function DashboardLayout({\n  children,\n  title,\n  subtitle\n}: DashboardLayoutProps) {\n  const router = useRouter();\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const { sidebarOpen } = useUIStore();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n  }, [user, isLoading, isAuthenticated, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar />\n      \n      <div\n        className={cn(\n          'transition-all duration-300 ease-in-out',\n          sidebarOpen ? 'lg:ml-64' : 'lg:ml-0'\n        )}\n      >\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-4 lg:p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {children}\n          </motion.div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAiBe,SAAS,gBAAgB,EACtC,QAAQ,EACR,KAAK,EACL,QAAQ,EACa;IACrB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAER,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2CACA,cAAc,aAAa;;kCAG7B,8OAAC,sIAAA,CAAA,UAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/dashboard/AppointmentCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { format } from 'date-fns';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { \n  Calendar, \n  Clock, \n  User, \n  MapPin, \n  FileText, \n  CheckCircle, \n  XCircle, \n  AlertCircle,\n  MoreHorizontal\n} from 'lucide-react';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { AppointmentWithDetails, AppointmentStatus } from '@/types';\nimport { useAuthStore } from '@/lib/store';\nimport { toast } from 'sonner';\n\ninterface AppointmentCardProps {\n  appointment: AppointmentWithDetails;\n  onStatusUpdate?: (appointmentId: string, status: AppointmentStatus) => void;\n  onViewDetails?: (appointment: AppointmentWithDetails) => void;\n}\n\nexport default function AppointmentCard({ \n  appointment, \n  onStatusUpdate, \n  onViewDetails \n}: AppointmentCardProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const { user } = useAuthStore();\n\n  const getStatusColor = (status: AppointmentStatus) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'approved':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'rejected':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getStatusIcon = (status: AppointmentStatus) => {\n    switch (status) {\n      case 'pending':\n        return <AlertCircle className=\"w-4 h-4\" />;\n      case 'approved':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'rejected':\n      case 'cancelled':\n        return <XCircle className=\"w-4 h-4\" />;\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      default:\n        return <AlertCircle className=\"w-4 h-4\" />;\n    }\n  };\n\n  const handleStatusUpdate = async (newStatus: AppointmentStatus) => {\n    if (!onStatusUpdate) return;\n\n    setIsLoading(true);\n    try {\n      await onStatusUpdate(appointment._id, newStatus);\n      toast.success(`Appointment ${newStatus} successfully`);\n    } catch (error) {\n      toast.error('Failed to update appointment status');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';\n    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';\n    return `${first}${last}`.toUpperCase();\n  };\n\n  const isDoctor = user?.role === 'doctor';\n  const canUpdateStatus = isDoctor && ['pending'].includes(appointment.status);\n  const displayUser = isDoctor ? appointment.patient : appointment.doctor;\n  const displayName = isDoctor\n    ? `${appointment.patient.firstName} ${appointment.patient.lastName}`\n    : `Dr. ${appointment.doctor.firstName} ${appointment.doctor.lastName}`;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      whileHover={{ y: -2 }}\n    >\n      <Card className=\"transition-all duration-300 hover:shadow-md\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-3\">\n              <Avatar className=\"w-12 h-12\">\n                <AvatarImage\n                  src={displayUser.profileImage}\n                  alt={displayName}\n                />\n                <AvatarFallback className=\"bg-blue-100 text-blue-600 font-semibold\">\n                  {getInitials(displayUser.firstName || '', displayUser.lastName || '')}\n                </AvatarFallback>\n              </Avatar>\n              \n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-semibold text-gray-900 truncate\">\n                  {displayName}\n                </h3>\n                \n                {!isDoctor && (\n                  <p className=\"text-sm text-gray-600\">\n                    {appointment.doctor.specialty}\n                  </p>\n                )}\n                \n                <div className=\"flex items-center mt-1\">\n                  <Badge \n                    variant=\"outline\" \n                    className={`${getStatusColor(appointment.status)} border`}\n                  >\n                    {getStatusIcon(appointment.status)}\n                    <span className=\"ml-1 capitalize\">{appointment.status}</span>\n                  </Badge>\n                </div>\n              </div>\n            </div>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuItem onClick={() => onViewDetails?.(appointment)}>\n                  <FileText className=\"mr-2 h-4 w-4\" />\n                  View Details\n                </DropdownMenuItem>\n                \n                {canUpdateStatus && (\n                  <>\n                    <DropdownMenuItem \n                      onClick={() => handleStatusUpdate('approved')}\n                      disabled={isLoading}\n                    >\n                      <CheckCircle className=\"mr-2 h-4 w-4 text-green-600\" />\n                      Approve\n                    </DropdownMenuItem>\n                    <DropdownMenuItem \n                      onClick={() => handleStatusUpdate('rejected')}\n                      disabled={isLoading}\n                    >\n                      <XCircle className=\"mr-2 h-4 w-4 text-red-600\" />\n                      Reject\n                    </DropdownMenuItem>\n                  </>\n                )}\n                \n                {appointment.status === 'pending' && !isDoctor && (\n                  <DropdownMenuItem \n                    onClick={() => handleStatusUpdate('cancelled')}\n                    disabled={isLoading}\n                  >\n                    <XCircle className=\"mr-2 h-4 w-4 text-red-600\" />\n                    Cancel\n                  </DropdownMenuItem>\n                )}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"pt-0\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Calendar className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>{format(new Date(appointment.dateTime), 'MMMM d, yyyy')}</span>\n            </div>\n\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Clock className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>{format(new Date(appointment.dateTime), 'h:mm a')}</span>\n            </div>\n\n            {!isDoctor && (\n              <div className=\"flex items-center text-sm text-gray-600\">\n                <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n                <span className=\"truncate\">\n                  {appointment.doctor.location.city}, {appointment.doctor.location.state}\n                </span>\n              </div>\n            )}\n\n            {appointment.symptoms && (\n              <div className=\"mt-3 p-2 bg-gray-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Symptoms: </span>\n                  {appointment.symptoms}\n                </p>\n              </div>\n            )}\n\n            {appointment.notes && (\n              <div className=\"mt-2 p-2 bg-blue-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Notes: </span>\n                  {appointment.notes}\n                </p>\n              </div>\n            )}\n\n            {appointment.diagnosis && (\n              <div className=\"mt-2 p-2 bg-green-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Diagnosis: </span>\n                  {appointment.diagnosis}\n                </p>\n              </div>\n            )}\n\n            {appointment.prescription && (\n              <div className=\"mt-2 p-2 bg-purple-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Prescription: </span>\n                  {appointment.prescription}\n                </p>\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AA5BA;;;;;;;;;;;;;AAoCe,SAAS,gBAAgB,EACtC,WAAW,EACX,cAAc,EACd,aAAa,EACQ;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,gBAAgB;QAErB,aAAa;QACb,IAAI;YACF,MAAM,eAAe,YAAY,GAAG,EAAE;YACtC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,UAAU,aAAa,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,MAAM,QAAQ,aAAa,UAAU,MAAM,GAAG,IAAI,UAAU,MAAM,CAAC,KAAK;QACxE,MAAM,OAAO,YAAY,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,CAAC,KAAK;QACpE,OAAO,GAAG,QAAQ,MAAM,CAAC,WAAW;IACtC;IAEA,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,kBAAkB,YAAY;QAAC;KAAU,CAAC,QAAQ,CAAC,YAAY,MAAM;IAC3E,MAAM,cAAc,WAAW,YAAY,OAAO,GAAG,YAAY,MAAM;IACvE,MAAM,cAAc,WAChB,GAAG,YAAY,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,QAAQ,EAAE,GAClE,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,QAAQ,EAAE;IAExE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,GAAG,CAAC;QAAE;kBAEpB,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDACV,KAAK,YAAY,YAAY;gDAC7B,KAAK;;;;;;0DAEP,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,YAAY,YAAY,SAAS,IAAI,IAAI,YAAY,QAAQ,IAAI;;;;;;;;;;;;kDAItE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX;;;;;;4CAGF,CAAC,0BACA,8OAAC;gDAAE,WAAU;0DACV,YAAY,MAAM,CAAC,SAAS;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAW,GAAG,eAAe,YAAY,MAAM,EAAE,OAAO,CAAC;;wDAExD,cAAc,YAAY,MAAM;sEACjC,8OAAC;4DAAK,WAAU;sEAAmB,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7D,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,gBAAgB;;kEAC/C,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAItC,iCACC;;kEACE,8OAAC,4IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,mBAAmB;wDAClC,UAAU;;0EAEV,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;kEAGzD,8OAAC,4IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,mBAAmB;wDAClC,UAAU;;0EAEV,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAA8B;;;;;;;;;4CAMtD,YAAY,MAAM,KAAK,aAAa,CAAC,0BACpC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,mBAAmB;gDAClC,UAAU;;kEAEV,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7D,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;;;;;;;;;;;;4BAG/C,CAAC,0BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;;4CACb,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI;4CAAC;4CAAG,YAAY,MAAM,CAAC,QAAQ,CAAC,KAAK;;;;;;;;;;;;;4BAK3E,YAAY,QAAQ,kBACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,QAAQ;;;;;;;;;;;;4BAK1B,YAAY,KAAK,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,KAAK;;;;;;;;;;;;4BAKvB,YAAY,SAAS,kBACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,SAAS;;;;;;;;;;;;4BAK3B,YAAY,YAAY,kBACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}, {"offset": {"line": 2412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/patient/appointments/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Calendar, Filter, Search } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport AppointmentCard from '@/components/dashboard/AppointmentCard';\nimport { AppointmentWithDetails, AppointmentStatus } from '@/types';\nimport { toast } from 'sonner';\n\nexport default function PatientAppointments() {\n  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);\n  const [filteredAppointments, setFilteredAppointments] = useState<AppointmentWithDetails[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n\n  useEffect(() => {\n    filterAppointments();\n  }, [appointments, searchQuery, statusFilter]);\n\n  const fetchAppointments = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/appointments?limit=100');\n      const result = await response.json();\n\n      if (result.success) {\n        setAppointments(result.data);\n      } else {\n        toast.error('Failed to fetch appointments');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch appointments');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterAppointments = () => {\n    let filtered = [...appointments];\n\n    // Filter by status\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(apt => apt.status === statusFilter);\n    }\n\n    // Filter by search query (doctor name)\n    if (searchQuery) {\n      filtered = filtered.filter(apt => \n        `${apt.doctor.user.profile.firstName} ${apt.doctor.user.profile.lastName}`\n          .toLowerCase()\n          .includes(searchQuery.toLowerCase()) ||\n        apt.doctor.specialty.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    // Sort by date (newest first)\n    filtered.sort((a, b) => new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime());\n\n    setFilteredAppointments(filtered);\n  };\n\n  const handleStatusUpdate = async (appointmentId: string, status: AppointmentStatus) => {\n    try {\n      const response = await fetch(`/api/appointments/${appointmentId}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ status }),\n      });\n\n      if (response.ok) {\n        fetchAppointments(); // Refresh appointments\n        toast.success(`Appointment ${status} successfully`);\n      } else {\n        throw new Error('Failed to update appointment');\n      }\n    } catch (error) {\n      toast.error('Failed to update appointment');\n      throw error;\n    }\n  };\n\n  const getStatusCounts = () => {\n    const pending = appointments.filter(apt => apt.status === 'pending').length;\n    const approved = appointments.filter(apt => apt.status === 'approved').length;\n    const completed = appointments.filter(apt => apt.status === 'completed').length;\n    const cancelled = appointments.filter(apt => apt.status === 'cancelled').length;\n    \n    return { pending, approved, completed, cancelled };\n  };\n\n  const statusCounts = getStatusCounts();\n\n  return (\n    <DashboardLayout \n      title=\"My Appointments\"\n      subtitle=\"View and manage your medical appointments\"\n    >\n      <div className=\"space-y-6\">\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-yellow-600\">{statusCounts.pending}</div>\n              <div className=\"text-sm text-gray-600\">Pending</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-green-600\">{statusCounts.approved}</div>\n              <div className=\"text-sm text-gray-600\">Approved</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-blue-600\">{statusCounts.completed}</div>\n              <div className=\"text-sm text-gray-600\">Completed</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-gray-600\">{statusCounts.cancelled}</div>\n              <div className=\"text-sm text-gray-600\">Cancelled</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Filter className=\"w-5 h-5\" />\n              <span>Filter Appointments</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search by doctor name or specialty...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              \n              <Select value={statusFilter} onValueChange={setStatusFilter}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Filter by status\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Appointments</SelectItem>\n                  <SelectItem value=\"pending\">Pending</SelectItem>\n                  <SelectItem value=\"approved\">Approved</SelectItem>\n                  <SelectItem value=\"completed\">Completed</SelectItem>\n                  <SelectItem value=\"cancelled\">Cancelled</SelectItem>\n                  <SelectItem value=\"rejected\">Rejected</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Appointments List */}\n        <Card>\n          <CardHeader>\n            <CardTitle>\n              Appointments ({filteredAppointments.length})\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"space-y-4\">\n                {Array.from({ length: 5 }).map((_, index) => (\n                  <div key={index} className=\"animate-pulse\">\n                    <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : filteredAppointments.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Calendar className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  No appointments found\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  {searchQuery || statusFilter !== 'all' \n                    ? 'Try adjusting your search or filter criteria'\n                    : 'You haven\\'t booked any appointments yet'\n                  }\n                </p>\n                <Button onClick={() => window.location.href = '/doctors'}>\n                  Book Your First Appointment\n                </Button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {filteredAppointments.map((appointment, index) => (\n                  <motion.div\n                    key={appointment._id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <AppointmentCard\n                      appointment={appointment}\n                      onStatusUpdate={handleStatusUpdate}\n                    />\n                  </motion.div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;QAAa;KAAa;IAE5C,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI;YAC7B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,WAAW;eAAI;SAAa;QAEhC,mBAAmB;QACnB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QACnD;QAEA,uCAAuC;QACvC,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CACvE,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW,OACnC,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEvE;QAEA,8BAA8B;QAC9B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;QAErF,wBAAwB;IAC1B;IAEA,MAAM,qBAAqB,OAAO,eAAuB;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,qBAAqB,uBAAuB;gBAC5C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;YACpD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;QAC3E,MAAM,WAAW,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,YAAY,MAAM;QAC7E,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;QAC/E,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;QAE/E,OAAO;YAAE;YAAS;YAAU;YAAW;QAAU;IACnD;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC,+IAAA,CAAA,UAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;kDAAsC,aAAa,OAAO;;;;;;kDACzE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;kDAAqC,aAAa,QAAQ;;;;;;kDACzE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;kDAAoC,aAAa,SAAS;;;;;;kDACzE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;kDAAoC,aAAa,SAAS;;;;;;kDACzE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAM7C,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;;oCAAC;oCACM,qBAAqB,MAAM;oCAAC;;;;;;;;;;;;sCAG/C,8OAAC,gIAAA,CAAA,cAAW;sCACT,wBACC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wCAAgB,WAAU;kDACzB,cAAA,8OAAC;4CAAI,WAAU;;;;;;uCADP;;;;;;;;;uCAKZ,qBAAqB,MAAM,KAAK,kBAClC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDACV,eAAe,iBAAiB,QAC7B,iDACA;;;;;;kDAGN,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;kDAAY;;;;;;;;;;;qDAK5D,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,aAAa,sBACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,kJAAA,CAAA,UAAe;4CACd,aAAa;4CACb,gBAAgB;;;;;;uCAPb,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBxC", "debugId": null}}]}