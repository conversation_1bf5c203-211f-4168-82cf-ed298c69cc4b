const { MongoClient } = require('mongodb');

async function fixDatabaseSchema() {
  console.log('🔧 Fixing Database Schema Issues...\n');

  // MongoDB connection
  const mongoUri = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/doctor-patient-portal';
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db();
    
    // Check for problematic patient records
    console.log('\n1️⃣ Checking for problematic patient records...');
    
    const patientsCollection = db.collection('patients');
    
    // Find records with 'user' field instead of 'userId'
    const recordsWithUserField = await patientsCollection.find({ user: { $exists: true } }).toArray();
    console.log(`Found ${recordsWithUserField.length} records with 'user' field`);
    
    // Find records with null userId
    const recordsWithNullUserId = await patientsCollection.find({ userId: null }).toArray();
    console.log(`Found ${recordsWithNullUserId.length} records with null userId`);
    
    // Find duplicate userId records
    const duplicateUserIds = await patientsCollection.aggregate([
      { $group: { _id: "$userId", count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } }
    ]).toArray();
    console.log(`Found ${duplicateUserIds.length} duplicate userId groups`);

    // Fix records with 'user' field
    if (recordsWithUserField.length > 0) {
      console.log('\n2️⃣ Fixing records with "user" field...');
      for (const record of recordsWithUserField) {
        if (record.user && !record.userId) {
          await patientsCollection.updateOne(
            { _id: record._id },
            { 
              $set: { userId: record.user },
              $unset: { user: "" }
            }
          );
          console.log(`✅ Fixed record ${record._id}: moved 'user' to 'userId'`);
        }
      }
    }

    // Remove records with null userId (these are likely corrupted)
    if (recordsWithNullUserId.length > 0) {
      console.log('\n3️⃣ Removing records with null userId...');
      const result = await patientsCollection.deleteMany({ userId: null });
      console.log(`✅ Removed ${result.deletedCount} records with null userId`);
    }

    // Handle duplicate userId records
    if (duplicateUserIds.length > 0) {
      console.log('\n4️⃣ Handling duplicate userId records...');
      for (const duplicate of duplicateUserIds) {
        if (duplicate._id) { // Skip null userId group
          const records = await patientsCollection.find({ userId: duplicate._id }).sort({ createdAt: -1 }).toArray();
          console.log(`Found ${records.length} records for userId ${duplicate._id}`);
          
          // Keep the most recent record, remove others
          for (let i = 1; i < records.length; i++) {
            await patientsCollection.deleteOne({ _id: records[i]._id });
            console.log(`✅ Removed duplicate record ${records[i]._id}`);
          }
        }
      }
    }

    // Check indexes
    console.log('\n5️⃣ Checking database indexes...');
    const indexes = await patientsCollection.indexes();
    console.log('Current indexes:');
    indexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
    });

    // Drop problematic indexes if they exist
    try {
      await patientsCollection.dropIndex('user_1');
      console.log('✅ Dropped problematic "user_1" index');
    } catch (error) {
      console.log('ℹ️  No "user_1" index to drop');
    }

    // Ensure correct index exists
    try {
      await patientsCollection.createIndex({ userId: 1 }, { unique: true });
      console.log('✅ Created unique index on userId');
    } catch (error) {
      console.log('ℹ️  userId index already exists');
    }

    console.log('\n6️⃣ Final verification...');
    const finalCount = await patientsCollection.countDocuments();
    const validRecords = await patientsCollection.countDocuments({ userId: { $exists: true, $ne: null } });
    console.log(`Total patient records: ${finalCount}`);
    console.log(`Valid records with userId: ${validRecords}`);

    if (finalCount === validRecords) {
      console.log('✅ All patient records are valid!');
    } else {
      console.log('⚠️  Some records may still have issues');
    }

  } catch (error) {
    console.error('❌ Error during schema fix:', error);
  } finally {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixDatabaseSchema().catch(console.error);
