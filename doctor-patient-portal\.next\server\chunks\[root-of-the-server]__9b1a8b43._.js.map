{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Doctor.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Doctor as IDoctor, AvailabilitySlot } from '@/types';\n\nexport interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}\n\nconst AvailabilitySlotSchema = new Schema<AvailabilitySlot>({\n  dayOfWeek: {\n    type: Number,\n    required: true,\n    min: 0,\n    max: 6,\n  },\n  startTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  endTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  isAvailable: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst LocationSchema = new Schema({\n  address: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  city: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  state: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n});\n\nconst DoctorSchema = new Schema<DoctorDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  specialty: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  bio: {\n    type: String,\n    required: true,\n    maxlength: 1000,\n  },\n  experience: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  rating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5,\n  },\n  totalRatings: {\n    type: Number,\n    default: 0,\n    min: 0,\n  },\n  profileImage: {\n    type: String,\n  },\n  availability: [AvailabilitySlotSchema],\n  consultationFee: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  location: {\n    type: LocationSchema,\n    required: true,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nDoctorSchema.index({ specialty: 1 });\nDoctorSchema.index({ rating: -1 });\nDoctorSchema.index({ 'location.city': 1 });\nDoctorSchema.index({ 'location.state': 1 });\n\n// Compound indexes for common queries\nDoctorSchema.index({ specialty: 1, rating: -1 });\nDoctorSchema.index({ 'location.city': 1, specialty: 1 });\n\nexport default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAmB;IAC1D,WAAW;QACT,MAAM;QACN,UAAU;QACV,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAiB;IAC9C,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,KAAK;QACH,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,cAAc;QACZ,MAAM;IACR;IACA,cAAc;QAAC;KAAuB;IACtC,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,QAAQ,CAAC;AAAE;AAChC,aAAa,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACxC,aAAa,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEzC,sCAAsC;AACtC,aAAa,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,iBAAiB;IAAG,WAAW;AAAE;uCAEvC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAiB,UAAU", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// User validation schemas\nexport const userProfileSchema = z.object({\n  firstName: z.string().min(1, 'First name is required').max(50),\n  lastName: z.string().min(1, 'Last name is required').max(50),\n  phone: z.string().optional(),\n  profileImage: z.string().url().optional(),\n  dateOfBirth: z.date().optional(),\n});\n\nexport const loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\nexport const registerSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  role: z.enum(['doctor', 'patient']),\n  profile: userProfileSchema,\n  doctorInfo: z.object({\n    specialty: z.string().min(1, 'Specialty is required'),\n    bio: z.string().min(10, 'Bio must be at least 10 characters').max(1000),\n    experience: z.number().min(0, 'Experience cannot be negative'),\n    consultationFee: z.number().min(0, 'Fee cannot be negative'),\n    location: z.object({\n      address: z.string().min(1, 'Address is required'),\n      city: z.string().min(1, 'City is required'),\n      state: z.string().min(1, 'State is required'),\n      zipCode: z.string().min(1, 'Zip code is required'),\n    }),\n  }).optional(),\n});\n\n// Doctor validation schemas\nexport const availabilitySlotSchema = z.object({\n  dayOfWeek: z.number().min(0).max(6),\n  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  isAvailable: z.boolean().default(true),\n});\n\nexport const doctorUpdateSchema = z.object({\n  specialty: z.string().min(1).optional(),\n  bio: z.string().min(10).max(1000).optional(),\n  experience: z.number().min(0).optional(),\n  consultationFee: z.number().min(0).optional(),\n  availability: z.array(availabilitySlotSchema).optional(),\n  location: z.object({\n    address: z.string().min(1),\n    city: z.string().min(1),\n    state: z.string().min(1),\n    zipCode: z.string().min(1),\n  }).optional(),\n});\n\n// Appointment validation schemas\nexport const bookAppointmentSchema = z.object({\n  doctorId: z.string().min(1, 'Doctor ID is required'),\n  dateTime: z.string().datetime('Invalid date format'),\n  symptoms: z.string().max(500).optional(),\n  notes: z.string().max(500).optional(),\n});\n\nexport const updateAppointmentSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  notes: z.string().max(500).optional(),\n  prescription: z.string().max(1000).optional(),\n  diagnosis: z.string().max(1000).optional(),\n});\n\n// Search and filter schemas\nexport const searchDoctorsSchema = z.object({\n  specialty: z.string().optional(),\n  location: z.string().optional(),\n  rating: z.number().min(0).max(5).optional(),\n  availability: z.boolean().optional(),\n  sortBy: z.enum(['rating', 'experience', 'fee', 'name']).default('rating'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\n// Query parameter validation\nexport const paginationSchema = z.object({\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\nexport const appointmentFilterSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  startDate: z.date().optional(),\n  endDate: z.date().optional(),\n  ...paginationSchema.shape,\n});\n\n// Utility function to validate and parse request body\nexport function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  const result = schema.safeParse(data);\n  if (!result.success) {\n    throw new Error(`Validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n\n// Utility function to validate query parameters\nexport function validateQueryParams<T>(schema: z.ZodSchema<T>, params: Record<string, any>): T {\n  // Convert string numbers to actual numbers for query params\n  const processedParams = Object.entries(params).reduce((acc, [key, value]) => {\n    if (value === undefined || value === null || value === '') {\n      return acc;\n    }\n    \n    // Try to convert string numbers to numbers\n    if (typeof value === 'string' && !isNaN(Number(value))) {\n      acc[key] = Number(value);\n    } else if (value === 'true') {\n      acc[key] = true;\n    } else if (value === 'false') {\n      acc[key] = false;\n    } else {\n      acc[key] = value;\n    }\n    \n    return acc;\n  }, {} as Record<string, any>);\n\n  const result = schema.safeParse(processedParams);\n  if (!result.success) {\n    throw new Error(`Query validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAGO,MAAM,oBAAoB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC;IAC3D,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC;IACzD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACvC,aAAa,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAChC;AAEO,MAAM,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,SAAS;IACT,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,sCAAsC,GAAG,CAAC;QAClE,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC9B,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACnC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC3B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACxB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACzB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B;IACF,GAAG,QAAQ;AACb;AAGO,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACjC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IACjE,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IAC/D,aAAa,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC;AAEO,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACrC,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ;IAC1C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACtC,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC3C,cAAc,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wBAAwB,QAAQ;IACtD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACxB,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACrB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACtB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1B,GAAG,QAAQ;AACb;AAGO,MAAM,wBAAwB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;AACrC;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACnC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;IAC3C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;AAC1C;AAGO,MAAM,sBAAsB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IACzC,cAAc,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAClC,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAc;QAAO;KAAO,EAAE,OAAO,CAAC;IAChE,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAGO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC5B,SAAS,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC1B,GAAG,iBAAiB,KAAK;AAC3B;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IAC3F;IACA,OAAO,OAAO,IAAI;AACpB;AAGO,SAAS,oBAAuB,MAAsB,EAAE,MAA2B;IACxF,4DAA4D;IAC5D,MAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;QACtE,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,OAAO;QACT;QAEA,2CAA2C;QAC3C,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,SAAS;YACtD,GAAG,CAAC,IAAI,GAAG,OAAO;QACpB,OAAO,IAAI,UAAU,QAAQ;YAC3B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO,IAAI,UAAU,SAAS;YAC5B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,GAAG,CAAC,IAAI,GAAG;QACb;QAEA,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IACjG;IACA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/doctors/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport Doctor from '@/models/Doctor';\nimport User from '@/models/User';\nimport { validateRequestBody, doctorUpdateSchema } from '@/lib/validations';\nimport { <PERSON><PERSON><PERSON><PERSON>ponse, DoctorWithUser } from '@/types';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    await connectDB();\n\n    const { id } = await params;\n    const doctor = await Doctor.findById(id)\n      .populate('userId', '-password')\n      .lean();\n\n    if (!doctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor not found',\n      }, { status: 404 });\n    }\n\n    const doctorWithUser: DoctorWithUser = {\n      ...doctor,\n      user: doctor.userId as any,\n    };\n\n    return NextResponse.json<ApiResponse<DoctorWithUser>>({\n      success: true,\n      data: doctorWithUser,\n    });\n\n  } catch (error) {\n    console.error('Get doctor error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to fetch doctor',\n    }, { status: 500 });\n  }\n}\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    await connectDB();\n\n    const { id } = await params;\n    const userId = request.headers.get('x-user-id');\n    const userRole = request.headers.get('x-user-role');\n\n    if (!userId || userRole !== 'doctor') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if the doctor profile belongs to the authenticated user\n    const doctor = await Doctor.findById(id);\n    if (!doctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor not found',\n      }, { status: 404 });\n    }\n\n    if (doctor.userId.toString() !== userId) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Forbidden: You can only update your own profile',\n      }, { status: 403 });\n    }\n\n    const body = await request.json();\n    const validatedData = validateRequestBody(doctorUpdateSchema, body);\n\n    // Update doctor profile\n    const updatedDoctor = await Doctor.findByIdAndUpdate(\n      id,\n      validatedData,\n      { new: true, runValidators: true }\n    ).populate('userId', '-password');\n\n    if (!updatedDoctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Failed to update doctor profile',\n      }, { status: 500 });\n    }\n\n    const doctorWithUser: DoctorWithUser = {\n      ...updatedDoctor.toObject(),\n      user: updatedDoctor.userId as any,\n    };\n\n    return NextResponse.json<ApiResponse<DoctorWithUser>>({\n      success: true,\n      data: doctorWithUser,\n      message: 'Doctor profile updated successfully',\n    });\n\n  } catch (error) {\n    console.error('Update doctor error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to update doctor profile',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,IAClC,QAAQ,CAAC,UAAU,aACnB,IAAI;QAEP,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,iBAAiC;YACrC,GAAG,MAAM;YACT,MAAM,OAAO,MAAM;QACrB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA8B;YACpD,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QAErC,IAAI,CAAC,UAAU,aAAa,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,gEAAgE;QAChE,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,OAAO,MAAM,CAAC,QAAQ,OAAO,QAAQ;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,2HAAA,CAAA,qBAAkB,EAAE;QAE9D,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,yHAAA,CAAA,UAAM,CAAC,iBAAiB,CAClD,IACA,eACA;YAAE,KAAK;YAAM,eAAe;QAAK,GACjC,QAAQ,CAAC,UAAU;QAErB,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,iBAAiC;YACrC,GAAG,cAAc,QAAQ,EAAE;YAC3B,MAAM,cAAc,MAAM;QAC5B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA8B;YACpD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}