import mongoose, { Document, Schema } from 'mongoose';
import { Appointment as IAppointment, AppointmentStatus } from '@/types';

export interface AppointmentDocument extends Document, Omit<IAppointment, '_id'> {}

const AppointmentSchema = new Schema<AppointmentDocument>({
  patientId: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
  },
  doctorId: {
    type: Schema.Types.ObjectId,
    ref: 'Doctor',
    required: true,
  },
  dateTime: {
    type: Date,
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'completed', 'cancelled'],
    default: 'pending',
  },
  notes: {
    type: String,
    maxlength: 500,
  },
  prescription: {
    type: String,
    maxlength: 1000,
  },
  symptoms: {
    type: String,
    maxlength: 500,
  },
  diagnosis: {
    type: String,
    maxlength: 1000,
  },
}, {
  timestamps: true,
});

// Indexes for better query performance
AppointmentSchema.index({ patientId: 1 });
AppointmentSchema.index({ doctorId: 1 });
AppointmentSchema.index({ dateTime: 1 });
AppointmentSchema.index({ status: 1 });

// Compound indexes for common queries
AppointmentSchema.index({ patientId: 1, status: 1 });
AppointmentSchema.index({ doctorId: 1, status: 1 });
AppointmentSchema.index({ doctorId: 1, dateTime: 1 });
AppointmentSchema.index({ patientId: 1, dateTime: -1 });

// Prevent double booking - same doctor, same time
AppointmentSchema.index(
  { doctorId: 1, dateTime: 1 },
  { 
    unique: true,
    partialFilterExpression: { 
      status: { $in: ['pending', 'approved'] } 
    }
  }
);

export default mongoose.models.Appointment || mongoose.model<AppointmentDocument>('Appointment', AppointmentSchema);
