import { z } from 'zod';

// User validation schemas
export const userProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  phone: z.string().optional(),
  profileImage: z.string().url().optional(),
  dateOfBirth: z.date().optional(),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['doctor', 'patient']),
  profile: userProfileSchema,
  doctorInfo: z.object({
    specialty: z.string().min(1, 'Specialty is required'),
    bio: z.string().min(10, 'Bio must be at least 10 characters').max(1000),
    experience: z.number().min(0, 'Experience cannot be negative'),
    consultationFee: z.number().min(0, 'Fee cannot be negative'),
    location: z.object({
      address: z.string().min(1, 'Address is required'),
      city: z.string().min(1, 'City is required'),
      state: z.string().min(1, 'State is required'),
      zipCode: z.string().min(1, 'Zip code is required'),
    }),
  }).optional(),
});

// Doctor validation schemas
export const availabilitySlotSchema = z.object({
  dayOfWeek: z.number().min(0).max(6),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  isAvailable: z.boolean().default(true),
});

export const doctorUpdateSchema = z.object({
  specialty: z.string().min(1).optional(),
  bio: z.string().min(10).max(1000).optional(),
  experience: z.number().min(0).optional(),
  consultationFee: z.number().min(0).optional(),
  availability: z.array(availabilitySlotSchema).optional(),
  location: z.object({
    address: z.string().min(1),
    city: z.string().min(1),
    state: z.string().min(1),
    zipCode: z.string().min(1),
  }).optional(),
});

// Appointment validation schemas
export const bookAppointmentSchema = z.object({
  doctorId: z.string().min(1, 'Doctor ID is required'),
  dateTime: z.string().datetime('Invalid date format'),
  symptoms: z.string().max(500).optional(),
  notes: z.string().max(500).optional(),
});

export const updateAppointmentSchema = z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),
  notes: z.string().max(500).optional(),
  prescription: z.string().max(1000).optional(),
  diagnosis: z.string().max(1000).optional(),
});

// Search and filter schemas
export const searchDoctorsSchema = z.object({
  specialty: z.string().optional(),
  location: z.string().optional(),
  rating: z.number().min(0).max(5).optional(),
  availability: z.boolean().optional(),
  sortBy: z.enum(['rating', 'experience', 'fee', 'name']).default('rating'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
});

// Query parameter validation
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
});

export const appointmentFilterSchema = z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  ...paginationSchema.shape,
});

// Utility function to validate and parse request body
export function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): T {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new Error(`Validation error: ${result.error.errors.map(e => e.message).join(', ')}`);
  }
  return result.data;
}

// Utility function to validate query parameters
export function validateQueryParams<T>(schema: z.ZodSchema<T>, params: Record<string, any>): T {
  // Convert string numbers to actual numbers for query params
  const processedParams = Object.entries(params).reduce((acc, [key, value]) => {
    if (value === undefined || value === null || value === '') {
      return acc;
    }
    
    // Try to convert string numbers to numbers
    if (typeof value === 'string' && !isNaN(Number(value))) {
      acc[key] = Number(value);
    } else if (value === 'true') {
      acc[key] = true;
    } else if (value === 'false') {
      acc[key] = false;
    } else {
      acc[key] = value;
    }
    
    return acc;
  }, {} as Record<string, any>);

  const result = schema.safeParse(processedParams);
  if (!result.success) {
    throw new Error(`Query validation error: ${result.error.errors.map(e => e.message).join(', ')}`);
  }
  return result.data;
}
