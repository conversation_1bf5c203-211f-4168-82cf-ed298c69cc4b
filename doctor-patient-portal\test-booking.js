/**
 * Test script to verify appointment booking functionality
 */

const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', data = null, token = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return {
      status: response.status,
      success: response.ok,
      data: result
    };
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    return {
      status: 500,
      success: false,
      error: error.message
    };
  }
}

// Test booking flow
async function testBookingFlow() {
  console.log('🧪 Testing Appointment Booking Flow...');
  console.log('=====================================');

  // Step 1: Test authentication endpoints
  console.log('\n1. Testing Authentication...');
  const authResponse = await makeRequest('/api/auth/me');
  console.log(`Auth check: ${authResponse.status} - ${authResponse.success ? 'Success' : 'Failed'}`);

  // Step 2: Test doctors endpoint
  console.log('\n2. Testing Doctors API...');
  const doctorsResponse = await makeRequest('/api/doctors');
  console.log(`Doctors API: ${doctorsResponse.status} - ${doctorsResponse.success ? 'Success' : 'Failed'}`);
  
  if (doctorsResponse.success && doctorsResponse.data.success) {
    const doctors = doctorsResponse.data.data;
    console.log(`Found ${doctors.length} doctors`);
    
    if (doctors.length > 0) {
      const firstDoctor = doctors[0];
      console.log(`First doctor: ${firstDoctor.firstName} ${firstDoctor.lastName} (${firstDoctor._id})`);
      
      // Step 3: Test appointment booking (without auth - should fail)
      console.log('\n3. Testing Appointment Booking (No Auth)...');
      const bookingData = {
        doctorId: firstDoctor._id,
        dateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        symptoms: 'Test symptoms',
        notes: 'Test booking'
      };
      
      const bookingResponse = await makeRequest('/api/appointments', 'POST', bookingData);
      console.log(`Booking without auth: ${bookingResponse.status} - ${bookingResponse.success ? 'Unexpected Success' : 'Expected Failure'}`);
      console.log(`Error message: ${bookingResponse.data.error}`);
    }
  }

  // Step 4: Test appointment listing
  console.log('\n4. Testing Appointments API...');
  const appointmentsResponse = await makeRequest('/api/appointments');
  console.log(`Appointments API: ${appointmentsResponse.status} - ${appointmentsResponse.success ? 'Success' : 'Failed'}`);

  console.log('\n📊 Test Summary:');
  console.log('================');
  console.log(`✅ Auth endpoint: ${authResponse.success ? 'Working' : 'Failed'}`);
  console.log(`✅ Doctors endpoint: ${doctorsResponse.success ? 'Working' : 'Failed'}`);
  console.log(`✅ Appointments endpoint: ${appointmentsResponse.success ? 'Working' : 'Failed'}`);
  console.log(`✅ Booking validation: ${!bookingResponse?.success ? 'Working (correctly rejected)' : 'Failed (should reject without auth)'}`);

  console.log('\n💡 Next Steps:');
  console.log('1. Login as a patient');
  console.log('2. Navigate to /doctors');
  console.log('3. Click on a doctor profile');
  console.log('4. Click "Book Appointment"');
  console.log('5. Fill out the booking form');
  console.log('6. Submit the appointment');
}

// Test validation schemas
async function testValidation() {
  console.log('\n🔍 Testing Validation...');
  
  // Test invalid booking data
  const invalidBookingData = {
    doctorId: '', // Invalid - empty
    dateTime: 'invalid-date', // Invalid format
    symptoms: 'a'.repeat(501), // Too long
  };
  
  const validationResponse = await makeRequest('/api/appointments', 'POST', invalidBookingData);
  console.log(`Validation test: ${validationResponse.status} - ${!validationResponse.success ? 'Working (correctly rejected)' : 'Failed'}`);
  
  if (!validationResponse.success) {
    console.log(`Validation error: ${validationResponse.data.error}`);
  }
}

// Run tests
async function runTests() {
  try {
    await testBookingFlow();
    await testValidation();
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Manual Testing Instructions:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Login as a patient');
    console.log('3. Go to /doctors');
    console.log('4. Select a doctor and click "Book Appointment"');
    console.log('5. Complete the booking form');
    console.log('6. Check the browser console and network tab for any errors');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

runTests();
