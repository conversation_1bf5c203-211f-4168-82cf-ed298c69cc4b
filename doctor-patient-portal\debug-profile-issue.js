const { MongoClient } = require('mongodb');

async function debugProfileIssue() {
  console.log('🔍 Debugging Patient Profile Update Issue...\n');

  // MongoDB connection
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db();
    
    // Check collections
    const collections = await db.listCollections().toArray();
    console.log('\n📋 Available Collections:');
    collections.forEach(col => console.log(`  - ${col.name}`));

    // Check users collection
    const usersCount = await db.collection('users').countDocuments();
    console.log(`\n👥 Users in database: ${usersCount}`);

    // Check patients collection
    const patientsCount = await db.collection('patients').countDocuments();
    console.log(`🏥 Patients in database: ${patientsCount}`);

    // Find sample user and patient data
    const sampleUser = await db.collection('users').findOne({ role: 'patient' });
    if (sampleUser) {
      console.log('\n👤 Sample Patient User:');
      console.log(`  - ID: ${sampleUser._id}`);
      console.log(`  - Email: ${sampleUser.email}`);
      console.log(`  - Role: ${sampleUser.role}`);
      console.log(`  - First Name: ${sampleUser.firstName}`);
      console.log(`  - Last Name: ${sampleUser.lastName}`);

      // Find corresponding patient profile
      const patientProfile = await db.collection('patients').findOne({ userId: sampleUser._id });
      if (patientProfile) {
        console.log('\n🏥 Corresponding Patient Profile:');
        console.log(`  - Patient ID: ${patientProfile._id}`);
        console.log(`  - User ID: ${patientProfile.userId}`);
        console.log(`  - First Name: ${patientProfile.firstName}`);
        console.log(`  - Last Name: ${patientProfile.lastName}`);
        console.log(`  - Phone: ${patientProfile.phone || 'Not set'}`);
        console.log(`  - Date of Birth: ${patientProfile.dateOfBirth || 'Not set'}`);
        console.log(`  - Gender: ${patientProfile.gender || 'Not set'}`);
        console.log(`  - Address: ${patientProfile.address || 'Not set'}`);
        console.log(`  - Emergency Contact: ${patientProfile.emergencyContact ? 'Set' : 'Not set'}`);
        console.log(`  - Medical History: ${patientProfile.medicalHistory ? 'Set' : 'Not set'}`);
        console.log(`  - Insurance: ${patientProfile.insurance ? 'Set' : 'Not set'}`);
      } else {
        console.log('\n❌ No patient profile found for this user');
      }
    } else {
      console.log('\n❌ No patient users found in database');
    }

    // Check for any validation errors in existing data
    console.log('\n🔍 Checking for data validation issues...');
    
    const patientsWithIssues = await db.collection('patients').find({
      $or: [
        { firstName: { $exists: false } },
        { lastName: { $exists: false } },
        { firstName: '' },
        { lastName: '' }
      ]
    }).toArray();

    if (patientsWithIssues.length > 0) {
      console.log(`⚠️  Found ${patientsWithIssues.length} patients with missing required fields`);
      patientsWithIssues.forEach(patient => {
        console.log(`  - Patient ${patient._id}: firstName="${patient.firstName}", lastName="${patient.lastName}"`);
      });
    } else {
      console.log('✅ All patients have required fields');
    }

    // Check for orphaned patient records
    const allPatients = await db.collection('patients').find({}).toArray();
    console.log(`\n🔗 Checking ${allPatients.length} patient records for orphaned data...`);
    
    let orphanedCount = 0;
    for (const patient of allPatients) {
      const user = await db.collection('users').findOne({ _id: patient.userId });
      if (!user) {
        console.log(`⚠️  Orphaned patient record: ${patient._id} (no corresponding user)`);
        orphanedCount++;
      }
    }
    
    if (orphanedCount === 0) {
      console.log('✅ No orphaned patient records found');
    } else {
      console.log(`❌ Found ${orphanedCount} orphaned patient records`);
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  } finally {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the debug function
debugProfileIssue().catch(console.error);
