// Use built-in fetch (Node.js 18+) or install node-fetch
const fetch = globalThis.fetch || require('node-fetch');

async function testProfileUpdate() {
  console.log('🧪 Testing Patient Profile Update API...\n');

  const baseUrl = 'http://localhost:3000';
  
  // Test data for a patient
  const testPatient = {
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Test',
    lastName: 'Patient',
    role: 'patient'
  };

  let authToken = null;

  try {
    // Step 1: Register a test patient
    console.log('1️⃣ Registering test patient...');
    const registerResponse = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPatient),
    });

    const registerResult = await registerResponse.json();
    
    if (registerResponse.ok && registerResult.success) {
      authToken = registerResult.data.token;
      console.log('✅ Patient registered successfully');
      console.log(`   Token: ${authToken.substring(0, 20)}...`);
    } else {
      // Try to login if user already exists
      console.log('⚠️  Registration failed, trying to login...');
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testPatient.email,
          password: testPatient.password
        }),
      });

      const loginResult = await loginResponse.json();
      
      if (loginResponse.ok && loginResult.success) {
        authToken = loginResult.data.token;
        console.log('✅ Patient logged in successfully');
        console.log(`   Token: ${authToken.substring(0, 20)}...`);
      } else {
        throw new Error(`Login failed: ${loginResult.error}`);
      }
    }

    // Step 2: Test GET patient profile
    console.log('\n2️⃣ Testing GET patient profile...');
    const getResponse = await fetch(`${baseUrl}/api/patients/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const getResult = await getResponse.json();
    console.log(`   Status: ${getResponse.status}`);
    console.log(`   Success: ${getResult.success}`);
    
    if (getResult.success) {
      console.log('✅ Profile retrieved successfully');
      console.log(`   Profile ID: ${getResult.data.id}`);
      console.log(`   Name: ${getResult.data.firstName} ${getResult.data.lastName}`);
    } else {
      console.log(`⚠️  Profile retrieval failed: ${getResult.error}`);
    }

    // Step 3: Test PUT patient profile update
    console.log('\n3️⃣ Testing PUT patient profile update...');
    
    const updateData = {
      firstName: 'Updated',
      lastName: 'Patient',
      phone: '+1-************',
      dateOfBirth: '1990-01-15',
      gender: 'male',
      address: '123 Test Street',
      city: 'Test City',
      state: 'TS',
      zipCode: '12345',
      emergencyContact: {
        name: 'Emergency Contact',
        relationship: 'Spouse',
        phone: '+1-************'
      },
      medicalHistory: {
        allergies: 'None',
        medications: 'None',
        conditions: 'None',
        surgeries: 'None'
      },
      insurance: {
        provider: 'Test Insurance',
        policyNumber: 'POL123456',
        groupNumber: 'GRP789'
      }
    };

    console.log('   Sending update data:');
    console.log(JSON.stringify(updateData, null, 4));

    const updateResponse = await fetch(`${baseUrl}/api/patients/me`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(updateData),
    });

    const updateResult = await updateResponse.json();
    console.log(`\n   Response Status: ${updateResponse.status}`);
    console.log(`   Response Success: ${updateResult.success}`);
    
    if (updateResult.success) {
      console.log('✅ Profile updated successfully!');
      console.log('   Updated data:');
      console.log(`     Name: ${updateResult.data.firstName} ${updateResult.data.lastName}`);
      console.log(`     Phone: ${updateResult.data.phone}`);
      console.log(`     DOB: ${updateResult.data.dateOfBirth}`);
      console.log(`     Gender: ${updateResult.data.gender}`);
      console.log(`     Address: ${updateResult.data.address}, ${updateResult.data.city}, ${updateResult.data.state} ${updateResult.data.zipCode}`);
      console.log(`     Emergency Contact: ${updateResult.data.emergencyContact?.name} (${updateResult.data.emergencyContact?.relationship})`);
      console.log(`     Insurance: ${updateResult.data.insurance?.provider}`);
    } else {
      console.log(`❌ Profile update failed: ${updateResult.error}`);
      console.log('   Full response:', JSON.stringify(updateResult, null, 2));
    }

    // Step 4: Verify the update by fetching again
    console.log('\n4️⃣ Verifying update by fetching profile again...');
    const verifyResponse = await fetch(`${baseUrl}/api/patients/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const verifyResult = await verifyResponse.json();
    
    if (verifyResult.success) {
      console.log('✅ Profile verification successful');
      console.log(`   Name: ${verifyResult.data.firstName} ${verifyResult.data.lastName}`);
      console.log(`   Phone: ${verifyResult.data.phone}`);
      console.log(`   DOB: ${verifyResult.data.dateOfBirth}`);
      console.log(`   Gender: ${verifyResult.data.gender}`);
      
      // Check if the update persisted
      if (verifyResult.data.firstName === 'Updated' && verifyResult.data.phone === '+1-************') {
        console.log('✅ Update persisted correctly!');
      } else {
        console.log('❌ Update did not persist correctly');
      }
    } else {
      console.log(`❌ Profile verification failed: ${verifyResult.error}`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testProfileUpdate().catch(console.error);
