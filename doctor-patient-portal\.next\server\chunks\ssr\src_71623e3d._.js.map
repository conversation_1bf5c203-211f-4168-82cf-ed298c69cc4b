{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\"\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button, buttonVariants } from \"@/components/ui/button\"\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  captionLayout = \"label\",\n  buttonVariant = \"ghost\",\n  formatters,\n  components,\n  ...props\n}: React.ComponentProps<typeof DayPicker> & {\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\n}) {\n  const defaultClassNames = getDefaultClassNames()\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\n        \"bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\n        className\n      )}\n      captionLayout={captionLayout}\n      formatters={{\n        formatMonthDropdown: (date) =>\n          date.toLocaleString(\"default\", { month: \"short\" }),\n        ...formatters,\n      }}\n      classNames={{\n        root: cn(\"w-fit\", defaultClassNames.root),\n        months: cn(\n          \"flex gap-4 flex-col md:flex-row relative\",\n          defaultClassNames.months\n        ),\n        month: cn(\"flex flex-col w-full gap-4\", defaultClassNames.month),\n        nav: cn(\n          \"flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between\",\n          defaultClassNames.nav\n        ),\n        button_previous: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_previous\n        ),\n        button_next: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_next\n        ),\n        month_caption: cn(\n          \"flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)\",\n          defaultClassNames.month_caption\n        ),\n        dropdowns: cn(\n          \"w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5\",\n          defaultClassNames.dropdowns\n        ),\n        dropdown_root: cn(\n          \"relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md\",\n          defaultClassNames.dropdown_root\n        ),\n        dropdown: cn(\"absolute inset-0 opacity-0\", defaultClassNames.dropdown),\n        caption_label: cn(\n          \"select-none font-medium\",\n          captionLayout === \"label\"\n            ? \"text-sm\"\n            : \"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5\",\n          defaultClassNames.caption_label\n        ),\n        table: \"w-full border-collapse\",\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\n        weekday: cn(\n          \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none\",\n          defaultClassNames.weekday\n        ),\n        week: cn(\"flex w-full mt-2\", defaultClassNames.week),\n        week_number_header: cn(\n          \"select-none w-(--cell-size)\",\n          defaultClassNames.week_number_header\n        ),\n        week_number: cn(\n          \"text-[0.8rem] select-none text-muted-foreground\",\n          defaultClassNames.week_number\n        ),\n        day: cn(\n          \"relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none\",\n          defaultClassNames.day\n        ),\n        range_start: cn(\n          \"rounded-l-md bg-accent\",\n          defaultClassNames.range_start\n        ),\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\n        range_end: cn(\"rounded-r-md bg-accent\", defaultClassNames.range_end),\n        today: cn(\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\n          defaultClassNames.today\n        ),\n        outside: cn(\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\n          defaultClassNames.outside\n        ),\n        disabled: cn(\n          \"text-muted-foreground opacity-50\",\n          defaultClassNames.disabled\n        ),\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\n        ...classNames,\n      }}\n      components={{\n        Root: ({ className, rootRef, ...props }) => {\n          return (\n            <div\n              data-slot=\"calendar\"\n              ref={rootRef}\n              className={cn(className)}\n              {...props}\n            />\n          )\n        },\n        Chevron: ({ className, orientation, ...props }) => {\n          if (orientation === \"left\") {\n            return (\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\n            )\n          }\n\n          if (orientation === \"right\") {\n            return (\n              <ChevronRightIcon\n                className={cn(\"size-4\", className)}\n                {...props}\n              />\n            )\n          }\n\n          return (\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\n          )\n        },\n        DayButton: CalendarDayButton,\n        WeekNumber: ({ children, ...props }) => {\n          return (\n            <td {...props}>\n              <div className=\"flex size-(--cell-size) items-center justify-center text-center\">\n                {children}\n              </div>\n            </td>\n          )\n        },\n        ...components,\n      }}\n      {...props}\n    />\n  )\n}\n\nfunction CalendarDayButton({\n  className,\n  day,\n  modifiers,\n  ...props\n}: React.ComponentProps<typeof DayButton>) {\n  const defaultClassNames = getDefaultClassNames()\n\n  const ref = React.useRef<HTMLButtonElement>(null)\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus()\n  }, [modifiers.focused])\n\n  return (\n    <Button\n      ref={ref}\n      variant=\"ghost\"\n      size=\"icon\"\n      data-day={day.date.toLocaleDateString()}\n      data-selected-single={\n        modifiers.selected &&\n        !modifiers.range_start &&\n        !modifiers.range_end &&\n        !modifiers.range_middle\n      }\n      data-range-start={modifiers.range_start}\n      data-range-end={modifiers.range_end}\n      data-range-middle={modifiers.range_middle}\n      className={cn(\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70\",\n        defaultClassNames.day,\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Calendar, CalendarDayButton }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAKA;AAAA;AAEA;AACA;AAXA;;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,8OAAC,kKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,QAAQ;YACrE,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,8OAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,8OAAC,wNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,8OAAC,0NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,8OAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;IACvC,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAqB;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;IACtC,GAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,o3BACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/dashboard/BookingForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { format, addDays, isSameDay, isAfter, isBefore, startOfDay } from 'date-fns';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Clock, Calendar as CalendarIcon, User, MapPin, DollarSign } from 'lucide-react';\nimport { DoctorWithUser } from '@/types';\nimport { toast } from 'sonner';\n\ninterface BookingFormProps {\n  doctor: <PERSON><PERSON><PERSON><PERSON>ser;\n}\n\nconst bookingSchema = z.object({\n  symptoms: z.string().max(500, 'Symptoms description is too long').optional(),\n  notes: z.string().max(500, 'Notes are too long').optional(),\n});\n\ntype BookingFormData = z.infer<typeof bookingSchema>;\n\n// Generate time slots for a day (9 AM to 5 PM, 30-minute intervals)\nconst generateTimeSlots = () => {\n  const slots = [];\n  for (let hour = 9; hour < 17; hour++) {\n    for (let minute = 0; minute < 60; minute += 30) {\n      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n      slots.push(time);\n    }\n  }\n  return slots;\n};\n\nexport default function BookingForm({ doctor }: BookingFormProps) {\n  const [selectedDate, setSelectedDate] = useState<Date | undefined>();\n  const [selectedTime, setSelectedTime] = useState<string>('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [step, setStep] = useState(1);\n  const router = useRouter();\n\n  const timeSlots = generateTimeSlots();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<BookingFormData>({\n    resolver: zodResolver(bookingSchema),\n  });\n\n  const handleDateSelect = (date: Date | undefined) => {\n    setSelectedDate(date);\n    setSelectedTime('');\n    if (date) {\n      setStep(2);\n    }\n  };\n\n  const handleTimeSelect = (time: string) => {\n    setSelectedTime(time);\n    setStep(3);\n  };\n\n  const onSubmit = async (data: BookingFormData) => {\n    if (!selectedDate || !selectedTime) {\n      toast.error('Please select a date and time');\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Create the appointment datetime\n      const [hours, minutes] = selectedTime.split(':').map(Number);\n      const appointmentDateTime = new Date(selectedDate);\n      appointmentDateTime.setHours(hours, minutes, 0, 0);\n\n      const bookingData = {\n        doctorId: doctor._id,\n        dateTime: appointmentDateTime.toISOString(),\n        symptoms: data.symptoms,\n        notes: data.notes,\n      };\n\n      const response = await fetch('/api/appointments', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n        },\n        body: JSON.stringify(bookingData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Failed to book appointment');\n      }\n\n      toast.success('Appointment booked successfully!');\n      router.push('/patient/appointments');\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to book appointment';\n      toast.error(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isDateDisabled = (date: Date) => {\n    const today = startOfDay(new Date());\n    const maxDate = addDays(today, 30); // Allow booking up to 30 days in advance\n    \n    return isBefore(date, today) || isAfter(date, maxDate);\n  };\n\n  // Show loading if doctor data is not available\n  if (!doctor) {\n    return (\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"animate-pulse space-y-4\">\n              <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Doctor Info */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <User className=\"w-5 h-5\" />\n            <span>Booking with Dr. {doctor?.firstName || 'Doctor'} {doctor?.lastName || ''}</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <Badge variant=\"secondary\">{doctor.specialty}</Badge>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <MapPin className=\"w-4 h-4\" />\n              <span>{doctor.location.city}, {doctor.location.state}</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <DollarSign className=\"w-4 h-4\" />\n              <span>${doctor.consultationFee} consultation fee</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Step Indicator */}\n      <div className=\"flex items-center justify-center space-x-4 mb-8\">\n        {[1, 2, 3].map((stepNumber) => (\n          <div key={stepNumber} className=\"flex items-center\">\n            <div\n              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                step >= stepNumber\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-200 text-gray-600'\n              }`}\n            >\n              {stepNumber}\n            </div>\n            {stepNumber < 3 && (\n              <div\n                className={`w-16 h-1 mx-2 ${\n                  step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'\n                }`}\n              />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Step 1: Date Selection */}\n      {step >= 1 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <CalendarIcon className=\"w-5 h-5\" />\n                <span>Select Date</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex justify-center\">\n                <Calendar\n                  mode=\"single\"\n                  selected={selectedDate}\n                  onSelect={handleDateSelect}\n                  disabled={isDateDisabled}\n                  className=\"rounded-md border\"\n                />\n              </div>\n              {selectedDate && (\n                <div className=\"mt-4 text-center\">\n                  <Badge variant=\"outline\" className=\"text-blue-700 border-blue-200\">\n                    Selected: {format(selectedDate, 'EEEE, MMMM d, yyyy')}\n                  </Badge>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n\n      {/* Step 2: Time Selection */}\n      {step >= 2 && selectedDate && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.1 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Clock className=\"w-5 h-5\" />\n                <span>Select Time</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3\">\n                {timeSlots.map((time) => (\n                  <Button\n                    key={time}\n                    variant={selectedTime === time ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => handleTimeSelect(time)}\n                    className=\"w-full\"\n                  >\n                    {time}\n                  </Button>\n                ))}\n              </div>\n              {selectedTime && (\n                <div className=\"mt-4 text-center\">\n                  <Badge variant=\"outline\" className=\"text-blue-700 border-blue-200\">\n                    Selected: {selectedTime}\n                  </Badge>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n\n      {/* Step 3: Additional Information */}\n      {step >= 3 && selectedDate && selectedTime && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.2 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>Additional Information</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"symptoms\">Symptoms (Optional)</Label>\n                  <Textarea\n                    id=\"symptoms\"\n                    placeholder=\"Describe your symptoms or reason for visit...\"\n                    {...register('symptoms')}\n                    className={errors.symptoms ? 'border-red-500' : ''}\n                  />\n                  {errors.symptoms && (\n                    <p className=\"text-sm text-red-500 mt-1\">{errors.symptoms.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <Label htmlFor=\"notes\">Additional Notes (Optional)</Label>\n                  <Textarea\n                    id=\"notes\"\n                    placeholder=\"Any additional information for the doctor...\"\n                    {...register('notes')}\n                    className={errors.notes ? 'border-red-500' : ''}\n                  />\n                  {errors.notes && (\n                    <p className=\"text-sm text-red-500 mt-1\">{errors.notes.message}</p>\n                  )}\n                </div>\n\n                {/* Booking Summary */}\n                <Alert>\n                  <AlertDescription>\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-semibold\">Booking Summary:</h4>\n                      <p><strong>Doctor:</strong> Dr. {doctor?.firstName || 'Doctor'} {doctor?.lastName || ''}</p>\n                      <p><strong>Date:</strong> {format(selectedDate, 'EEEE, MMMM d, yyyy')}</p>\n                      <p><strong>Time:</strong> {selectedTime}</p>\n                      <p><strong>Fee:</strong> ${doctor.consultationFee}</p>\n                    </div>\n                  </AlertDescription>\n                </Alert>\n\n                <div className=\"flex space-x-4\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    onClick={() => setStep(2)}\n                    className=\"flex-1\"\n                  >\n                    Back\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    disabled={isLoading}\n                    className=\"flex-1 bg-blue-600 hover:bg-blue-700\"\n                  >\n                    {isLoading ? 'Booking...' : 'Book Appointment'}\n                  </Button>\n                </div>\n              </form>\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAlBA;;;;;;;;;;;;;;;;;;AAwBA,MAAM,gBAAgB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,oCAAoC,QAAQ;IAC1E,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,sBAAsB,QAAQ;AAC3D;AAIA,oEAAoE;AACpE,MAAM,oBAAoB;IACxB,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,OAAQ;QACpC,IAAK,IAAI,SAAS,GAAG,SAAS,IAAI,UAAU,GAAI;YAC9C,MAAM,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;YACxF,MAAM,IAAI,CAAC;QACb;IACF;IACA,OAAO;AACT;AAEe,SAAS,YAAY,EAAE,MAAM,EAAoB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY;IAElB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB;QAChB,IAAI,MAAM;YACR,QAAQ;QACV;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,QAAQ;IACV;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,kCAAkC;YAClC,MAAM,CAAC,OAAO,QAAQ,GAAG,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC;YACrD,MAAM,sBAAsB,IAAI,KAAK;YACrC,oBAAoB,QAAQ,CAAC,OAAO,SAAS,GAAG;YAEhD,MAAM,cAAc;gBAClB,UAAU,OAAO,GAAG;gBACpB,UAAU,oBAAoB,WAAW;gBACzC,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;YACnB;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;gBAChE;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,IAAI;QAC7B,MAAM,UAAU,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,KAAK,yCAAyC;QAE7E,OAAO,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAChD;IAEA,+CAA+C;IAC/C,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;wCAAK;wCAAkB,QAAQ,aAAa;wCAAS;wCAAE,QAAQ,YAAY;;;;;;;;;;;;;;;;;;kCAGhF,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa,OAAO,SAAS;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;;gDAAM,OAAO,QAAQ,CAAC,IAAI;gDAAC;gDAAG,OAAO,QAAQ,CAAC,KAAK;;;;;;;;;;;;;8CAEtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;;gDAAK;gDAAE,OAAO,eAAe;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,2BACd,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCACC,WAAW,CAAC,0EAA0E,EACpF,QAAQ,aACJ,2BACA,6BACJ;0CAED;;;;;;4BAEF,aAAa,mBACZ,8OAAC;gCACC,WAAW,CAAC,cAAc,EACxB,OAAO,aAAa,gBAAgB,eACpC;;;;;;;uBAdE;;;;;;;;;;YAsBb,QAAQ,mBACP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU;wCACV,UAAU;wCACV,UAAU;wCACV,WAAU;;;;;;;;;;;gCAGb,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAgC;4CACtD,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU7C,QAAQ,KAAK,8BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,iBAAiB,OAAO,YAAY;4CAC7C,MAAK;4CACL,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAET;2CANI;;;;;;;;;;gCAUV,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAgC;4CACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxB,QAAQ,KAAK,gBAAgB,8BAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU,aAAa;gCAAW,WAAU;;kDAChD,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACX,GAAG,SAAS,WAAW;gDACxB,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4CAEjD,OAAO,QAAQ,kBACd,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kDAIrE,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACX,GAAG,SAAS,QAAQ;gDACrB,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;4CAE9C,OAAO,KAAK,kBACX,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kDAKlE,8OAAC,iIAAA,CAAA,QAAK;kDACJ,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;sDACf,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAgB;4DAAM,QAAQ,aAAa;4DAAS;4DAAE,QAAQ,YAAY;;;;;;;kEACrF,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAc;4DAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;;kEAChD,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAc;4DAAE;;;;;;;kEAC3B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAa;4DAAG,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;kDAKvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,QAAQ;gDACvB,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/patient/book/%5BdoctorId%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, use } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ArrowLeft } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport BookingForm from '@/components/dashboard/BookingForm';\nimport { DoctorWithUser } from '@/types';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { toast } from 'sonner';\n\ninterface BookingPageProps {\n  params: Promise<{ doctorId: string }>;\n}\n\nexport default function BookingPage({ params }: BookingPageProps) {\n  const [doctor, setDoctor] = useState<DoctorWithUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n  const { isAuthenticated, user } = useAuth();\n\n  // Unwrap params using React.use()\n  const { doctorId } = use(params);\n\n  useEffect(() => {\n    // Check if user is authenticated and is a patient\n    if (!isAuthenticated) {\n      router.push('/login');\n      return;\n    }\n\n    if (user?.role !== 'patient') {\n      toast.error('Only patients can book appointments');\n      router.push('/doctors');\n      return;\n    }\n\n    const fetchDoctor = async () => {\n      try {\n        const response = await fetch(`/api/doctors/${doctorId}`);\n        const result = await response.json();\n\n        if (result.success) {\n          setDoctor(result.data);\n        } else {\n          toast.error('Doctor not found');\n          router.push('/doctors');\n        }\n      } catch (error) {\n        toast.error('Failed to fetch doctor information');\n        router.push('/doctors');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDoctor();\n  }, [doctorId, router, isAuthenticated, user]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!doctor) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Doctor not found</h2>\n          <Button onClick={() => router.push('/doctors')}>\n            Back to Doctors\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            onClick={() => router.back()}\n            className=\"mb-4\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Back\n          </Button>\n\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Book Appointment</h1>\n            <p className=\"mt-2 text-gray-600\">\n              Schedule your consultation with Dr. {doctor.firstName} {doctor.lastName}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8\">\n        <BookingForm doctor={doctor} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AATA;;;;;;;;;AAee,SAAS,YAAY,EAAE,MAAM,EAAoB;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IAExC,kCAAkC;IAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM,SAAS,WAAW;YAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,UAAU;gBACvD,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,OAAO,IAAI;gBACvB,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAU;QAAQ;QAAiB;KAAK;IAE5C,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;kCAAa;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;;wCAAqB;wCACK,OAAO,SAAS;wCAAC;wCAAE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAO/E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,UAAW;oBAAC,QAAQ;;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}]}