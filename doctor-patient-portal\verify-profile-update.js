/**
 * <PERSON><PERSON><PERSON> to verify profile update functionality is working correctly
 * This script will test the actual API endpoints with real data
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', data = null, token = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return {
      status: response.status,
      success: response.ok,
      data: result
    };
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    return {
      status: 500,
      success: false,
      error: error.message
    };
  }
}

// Function to get user input
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Test login and get auth token
async function testLogin() {
  console.log('\n🔐 Testing Login...');
  
  const email = await askQuestion('Enter your email: ');
  const password = await askQuestion('Enter your password: ');
  
  const loginResponse = await makeRequest('/api/auth/login', 'POST', {
    email,
    password
  });
  
  if (loginResponse.success && loginResponse.data.success) {
    console.log('✅ Login successful!');
    return loginResponse.data.data.token;
  } else {
    console.log('❌ Login failed:', loginResponse.data.error);
    return null;
  }
}

// Test profile retrieval
async function testGetProfile(token, userRole) {
  console.log(`\n📋 Testing ${userRole} Profile Retrieval...`);
  
  const endpoint = userRole === 'doctor' ? '/api/doctors/me' : '/api/patients/me';
  const response = await makeRequest(endpoint, 'GET', null, token);
  
  if (response.success && response.data.success) {
    console.log('✅ Profile retrieved successfully!');
    console.log('Current profile data:');
    console.log(JSON.stringify(response.data.data, null, 2));
    return response.data.data;
  } else {
    console.log('❌ Failed to retrieve profile:', response.data.error);
    return null;
  }
}

// Test profile update
async function testUpdateProfile(token, userRole, currentProfile) {
  console.log(`\n✏️ Testing ${userRole} Profile Update...`);
  
  let updateData;
  
  if (userRole === 'doctor') {
    // Test doctor profile update
    updateData = {
      specialty: currentProfile.specialty || 'General Practice',
      bio: `Updated bio at ${new Date().toISOString()}`,
      experience: (currentProfile.experience || 0) + 1,
      consultationFee: (currentProfile.consultationFee || 100) + 10,
      location: {
        address: currentProfile.location?.address || '123 Test St',
        city: currentProfile.location?.city || 'Test City',
        state: currentProfile.location?.state || 'TS',
        zipCode: currentProfile.location?.zipCode || '12345'
      },
      qualifications: currentProfile.qualifications || ['MD']
    };
  } else {
    // Test patient profile update
    updateData = {
      firstName: currentProfile.firstName || 'Test',
      lastName: currentProfile.lastName || 'Patient',
      phone: '+****************',
      dateOfBirth: currentProfile.dateOfBirth || '1990-01-01',
      gender: currentProfile.gender || 'prefer-not-to-say',
      address: '456 Test Ave',
      city: 'Test City',
      state: 'TS',
      zipCode: '54321',
      emergencyContact: {
        name: 'Emergency Contact',
        relationship: 'Friend',
        phone: '+****************'
      },
      medicalHistory: {
        allergies: 'None',
        medications: 'None',
        conditions: 'None',
        surgeries: 'None'
      },
      insurance: {
        provider: 'Test Insurance',
        policyNumber: 'TEST123',
        groupNumber: 'GRP456'
      }
    };
  }
  
  console.log('Updating profile with data:');
  console.log(JSON.stringify(updateData, null, 2));
  
  const endpoint = userRole === 'doctor' ? '/api/doctors/me' : '/api/patients/me';
  const response = await makeRequest(endpoint, 'PUT', updateData, token);
  
  if (response.success && response.data.success) {
    console.log('✅ Profile updated successfully!');
    console.log('Updated profile data:');
    console.log(JSON.stringify(response.data.data, null, 2));
    return true;
  } else {
    console.log('❌ Failed to update profile:', response.data.error);
    return false;
  }
}

// Test profile persistence
async function testProfilePersistence(token, userRole) {
  console.log(`\n🔄 Testing ${userRole} Profile Persistence...`);
  
  const endpoint = userRole === 'doctor' ? '/api/doctors/me' : '/api/patients/me';
  const response = await makeRequest(endpoint, 'GET', null, token);
  
  if (response.success && response.data.success) {
    console.log('✅ Profile data persisted successfully!');
    console.log('Persisted profile data:');
    console.log(JSON.stringify(response.data.data, null, 2));
    return true;
  } else {
    console.log('❌ Failed to retrieve updated profile:', response.data.error);
    return false;
  }
}

// Main test function
async function runProfileUpdateTest() {
  console.log('🚀 Starting Profile Update Verification...');
  console.log('==========================================');
  
  try {
    // Step 1: Login
    const token = await testLogin();
    if (!token) {
      console.log('❌ Cannot proceed without authentication token');
      rl.close();
      return;
    }
    
    // Step 2: Get user info to determine role
    const userResponse = await makeRequest('/api/auth/me', 'GET', null, token);
    if (!userResponse.success || !userResponse.data.success) {
      console.log('❌ Failed to get user info');
      rl.close();
      return;
    }
    
    const userRole = userResponse.data.data.user.role;
    console.log(`\n👤 User role: ${userRole}`);
    
    // Step 3: Get current profile
    const currentProfile = await testGetProfile(token, userRole);
    if (!currentProfile) {
      console.log('❌ Cannot proceed without current profile data');
      rl.close();
      return;
    }
    
    // Step 4: Update profile
    const updateSuccess = await testUpdateProfile(token, userRole, currentProfile);
    if (!updateSuccess) {
      console.log('❌ Profile update failed');
      rl.close();
      return;
    }
    
    // Step 5: Verify persistence
    const persistenceSuccess = await testProfilePersistence(token, userRole);
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('✅ Login: Success');
    console.log('✅ Profile Retrieval: Success');
    console.log(`${updateSuccess ? '✅' : '❌'} Profile Update: ${updateSuccess ? 'Success' : 'Failed'}`);
    console.log(`${persistenceSuccess ? '✅' : '❌'} Data Persistence: ${persistenceSuccess ? 'Success' : 'Failed'}`);
    
    if (updateSuccess && persistenceSuccess) {
      console.log('\n🎉 Profile update functionality is working correctly!');
      console.log('✅ Changes are being saved to the database');
      console.log('✅ Data persists after updates');
    } else {
      console.log('\n❌ Profile update functionality has issues');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    rl.close();
  }
}

// Run the test
runProfileUpdateTest();
