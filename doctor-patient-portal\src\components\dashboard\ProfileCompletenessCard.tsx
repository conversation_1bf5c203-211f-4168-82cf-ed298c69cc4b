'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, User, ArrowRight } from 'lucide-react';

interface ProfileCompleteness {
  isComplete: boolean;
  missingFields: string[];
  completionPercentage: number;
}

export default function ProfileCompletenessCard() {
  const [completeness, setCompleteness] = useState<ProfileCompleteness | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkProfileCompleteness();
  }, []);

  const checkProfileCompleteness = async () => {
    try {
      const response = await fetch('/api/patients/profile-completeness', {
        headers: {
          'Authorization': `Bear<PERSON> ${localStorage.getItem('authToken')}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCompleteness(result.data);
        }
      }
    } catch (error) {
      console.error('Error checking profile completeness:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-2 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!completeness) {
    return null;
  }

  return (
    <Card className={`${completeness.isComplete ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg">
          {completeness.isComplete ? (
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 text-orange-600 mr-2" />
          )}
          Profile Completeness
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span className="font-medium">{completeness.completionPercentage}%</span>
          </div>
          <Progress 
            value={completeness.completionPercentage} 
            className={`h-2 ${completeness.isComplete ? 'bg-green-100' : 'bg-orange-100'}`}
          />
        </div>

        {completeness.isComplete ? (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Your profile is complete! You can now book appointments with doctors.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-3">
            <Alert className="border-orange-200 bg-orange-50">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                Please complete your profile to book appointments.
              </AlertDescription>
            </Alert>
            
            {completeness.missingFields.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">Missing information:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  {completeness.missingFields.map((field, index) => (
                    <li key={index} className="flex items-center">
                      <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mr-2"></div>
                      {field}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <Button 
              onClick={() => router.push('/patient/profile')}
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              <User className="w-4 h-4 mr-2" />
              Complete Profile
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
