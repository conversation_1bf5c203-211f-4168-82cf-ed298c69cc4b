import { PatientDocument } from '@/models/Patient';

export interface ProfileCompleteness {
  isComplete: boolean;
  missingFields: string[];
  completionPercentage: number;
}

/**
 * Check if a patient profile is complete enough for booking appointments
 * @param patient - The patient document from the database
 * @returns ProfileCompleteness object with validation results
 */
export function validatePatientProfileCompleteness(patient: PatientDocument | null): ProfileCompleteness {
  if (!patient) {
    return {
      isComplete: false,
      missingFields: ['Patient profile not found'],
      completionPercentage: 0
    };
  }

  const requiredFields = [
    { field: 'firstName', label: 'First Name' },
    { field: 'lastName', label: 'Last Name' },
    { field: 'phone', label: 'Phone Number' },
    { field: 'dateOfBirth', label: 'Date of Birth' },
    { field: 'gender', label: 'Gender' }
  ];

  const recommendedFields = [
    { field: 'address', label: 'Address' },
    { field: 'city', label: 'City' },
    { field: 'state', label: 'State' },
    { field: 'zipCode', label: 'ZIP Code' },
    { field: 'emergencyContact.name', label: 'Emergency Contact Name' },
    { field: 'emergencyContact.phone', label: 'Emergency Contact Phone' }
  ];

  const missingFields: string[] = [];
  let completedFields = 0;
  const totalFields = requiredFields.length + recommendedFields.length;

  // Check required fields
  for (const { field, label } of requiredFields) {
    const value = getNestedValue(patient, field);
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(label);
    } else {
      completedFields++;
    }
  }

  // Check recommended fields (for completion percentage)
  for (const { field } of recommendedFields) {
    const value = getNestedValue(patient, field);
    if (value && (typeof value !== 'string' || value.trim() !== '')) {
      completedFields++;
    }
  }

  const completionPercentage = Math.round((completedFields / totalFields) * 100);
  const isComplete = missingFields.length === 0;

  return {
    isComplete,
    missingFields,
    completionPercentage
  };
}

/**
 * Get nested value from object using dot notation
 * @param obj - The object to search in
 * @param path - The dot-separated path (e.g., 'emergencyContact.name')
 * @returns The value at the specified path
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
}

/**
 * Get user-friendly error message for incomplete profile
 * @param missingFields - Array of missing field labels
 * @returns Formatted error message
 */
export function getProfileIncompleteMessage(missingFields: string[]): string {
  if (missingFields.length === 0) {
    return '';
  }

  if (missingFields.length === 1) {
    return `Please complete your profile by adding: ${missingFields[0]}`;
  }

  if (missingFields.length === 2) {
    return `Please complete your profile by adding: ${missingFields.join(' and ')}`;
  }

  const lastField = missingFields.pop();
  return `Please complete your profile by adding: ${missingFields.join(', ')}, and ${lastField}`;
}
