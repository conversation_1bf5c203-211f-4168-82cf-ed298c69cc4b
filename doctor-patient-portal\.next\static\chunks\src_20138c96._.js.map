{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/logo.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Stethoscope } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n  className?: string;\n  clickable?: boolean;\n  variant?: 'default' | 'compact';\n}\n\nexport default function Logo({ \n  size = 'md', \n  showText = true, \n  className,\n  clickable = true,\n  variant = 'default'\n}: LogoProps) {\n  const router = useRouter();\n\n  const sizeClasses = {\n    sm: {\n      icon: 'w-4 h-4',\n      container: 'p-1.5',\n      text: 'text-sm',\n      subtext: 'text-xs'\n    },\n    md: {\n      icon: 'w-6 h-6',\n      container: 'p-2',\n      text: 'text-lg',\n      subtext: 'text-xs'\n    },\n    lg: {\n      icon: 'w-8 h-8',\n      container: 'p-3',\n      text: 'text-xl',\n      subtext: 'text-sm'\n    }\n  };\n\n  const handleClick = () => {\n    if (clickable) {\n      router.push('/');\n    }\n  };\n\n  const logoContent = (\n    <div className={cn(\n      'flex items-center space-x-2',\n      clickable && 'cursor-pointer group',\n      className\n    )}>\n      <motion.div \n        className={cn(\n          'bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg shadow-sm',\n          sizeClasses[size].container,\n          clickable && 'group-hover:shadow-md transition-shadow duration-200'\n        )}\n        whileHover={clickable ? { scale: 1.05 } : {}}\n        whileTap={clickable ? { scale: 0.95 } : {}}\n      >\n        <Stethoscope className={cn('text-white', sizeClasses[size].icon)} />\n      </motion.div>\n      \n      {showText && (\n        <div className={cn(\n          'flex flex-col',\n          variant === 'compact' && 'hidden sm:flex'\n        )}>\n          <h1 className={cn(\n            'font-bold text-gray-900 leading-tight',\n            sizeClasses[size].text,\n            clickable && 'group-hover:text-blue-600 transition-colors duration-200'\n          )}>\n            HealthCare\n          </h1>\n          {variant === 'default' && (\n            <p className={cn(\n              'text-gray-500 leading-tight',\n              sizeClasses[size].subtext\n            )}>\n              Portal\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n\n  if (clickable) {\n    return (\n      <motion.div\n        onClick={handleClick}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className=\"inline-block\"\n      >\n        {logoContent}\n      </motion.div>\n    );\n  }\n\n  return logoContent;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAee,SAAS,KAAK,EAC3B,OAAO,IAAI,EACX,WAAW,IAAI,EACf,SAAS,EACT,YAAY,IAAI,EAChB,UAAU,SAAS,EACT;;IACV,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc;QAClB,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,aAAa,wBACb;;0BAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WAAW,CAAC,KAAK,CAAC,SAAS,EAC3B,aAAa;gBAEf,YAAY,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAC3C,UAAU,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;0BAEzC,cAAA,6LAAC,mNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,WAAW,CAAC,KAAK,CAAC,IAAI;;;;;;;;;;;YAGhE,0BACC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iBACA,YAAY,aAAa;;kCAEzB,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,yCACA,WAAW,CAAC,KAAK,CAAC,IAAI,EACtB,aAAa;kCACZ;;;;;;oBAGF,YAAY,2BACX,6LAAC;wBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,+BACA,WAAW,CAAC,KAAK,CAAC,OAAO;kCACxB;;;;;;;;;;;;;;;;;;IASb,IAAI,WAAW;QACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;YACT,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,WAAU;sBAET;;;;;;IAGP;IAEA,OAAO;AACT;GA7FwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Home,\n  Calendar,\n  Users,\n  User,\n  LogOut,\n  Menu,\n  X,\n  ClipboardList,\n  Search,\n  Bell,\n} from 'lucide-react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { toast } from 'sonner';\nimport Logo from '@/components/ui/logo';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport default function Sidebar({ className }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const isDoctor = user?.role === 'doctor';\n\n  const doctorNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/doctor/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Appointments',\n      href: '/doctor/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/doctor/profile',\n      icon: User,\n    },\n  ];\n\n  const patientNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/patient/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Find Doctors',\n      href: '/doctors',\n      icon: Search,\n    },\n    {\n      title: 'My Appointments',\n      href: '/patient/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/patient/profile',\n      icon: User,\n    },\n  ];\n\n  const navItems = isDoctor ? doctorNavItems : patientNavItems;\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      await logout();\n      toast.success('Logged out successfully');\n    } catch (error) {\n      toast.error('Failed to logout');\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n    closed: {\n      x: '-100%',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n  };\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        variants={sidebarVariants}\n        animate={sidebarOpen ? 'open' : 'closed'}\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-xl lg:relative lg:translate-x-0 lg:shadow-none',\n          'flex flex-col',\n          className\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <Logo size=\"md\" variant=\"default\" />\n\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleSidebar}\n              className=\"lg:hidden hover:bg-gray-100 transition-colors\"\n            >\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 p-4 space-y-1 overflow-y-auto\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              const Icon = item.icon;\n\n              return (\n                <motion.div\n                  key={item.href}\n                  whileHover={{ x: 2 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"relative\"\n                >\n                  <Button\n                    variant={isActive ? 'default' : 'ghost'}\n                    size=\"default\"\n                    className={cn(\n                      'w-full justify-start text-left h-11 px-3 rounded-lg transition-all duration-200',\n                      isActive\n                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm hover:from-blue-700 hover:to-blue-800'\n                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                    )}\n                    onClick={() => {\n                      router.push(item.href);\n                      if (typeof window !== 'undefined' && window.innerWidth < 1024) {\n                        toggleSidebar();\n                      }\n                    }}\n                  >\n                    <Icon className={cn(\n                      'w-5 h-5 mr-3 flex-shrink-0',\n                      isActive ? 'text-white' : 'text-gray-500'\n                    )} />\n                    <span className=\"truncate\">{item.title}</span>\n                  </Button>\n\n                  {isActive && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className=\"absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-r-full\"\n                      initial={false}\n                      transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                    />\n                  )}\n                </motion.div>\n              );\n            })}\n          </nav>\n\n          {/* User Profile */}\n          <div className=\"p-4 border-t border-gray-200 bg-gray-50/50\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start p-3 h-auto rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200\"\n                >\n                  <div className=\"flex items-center space-x-3 w-full\">\n                    <Avatar className=\"w-10 h-10 ring-2 ring-blue-100\">\n                      <AvatarImage\n                        src={user?.profileImage}\n                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}\n                      />\n                      <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold\">\n                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}\n                      </AvatarFallback>\n                    </Avatar>\n\n                    <div className=\"flex-1 text-left min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}\n                      </p>\n                      <p className=\"text-xs text-gray-500 capitalize\">\n                        {user?.role || 'User'}\n                      </p>\n                    </div>\n\n                    <div className=\"text-gray-400\">\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </div>\n                  </div>\n                </Button>\n              </DropdownMenuTrigger>\n              \n              <DropdownMenuContent align=\"end\" className=\"w-56 shadow-lg border-0 bg-white\">\n                <DropdownMenuItem\n                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}\n                  className=\"cursor-pointer hover:bg-gray-50 transition-colors\"\n                >\n                  <User className=\"mr-2 h-4 w-4 text-gray-500\" />\n                  <span>Profile</span>\n                </DropdownMenuItem>\n\n                <DropdownMenuSeparator className=\"bg-gray-100\" />\n\n                <DropdownMenuItem\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"cursor-pointer text-red-600 hover:bg-red-50 hover:text-red-700 focus:text-red-700 transition-colors\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </motion.aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;;;AA9BA;;;;;;;;;;;;;AAoCe,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,MAAM,SAAS;IAEhC,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,WAAW,WAAW,iBAAiB;IAE7C,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE;;0BAEE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+HACA,iBACA;0BAGF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,UAAI;oCAAC,MAAK;oCAAK,SAAQ;;;;;;8CAExB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,YAAY;4CAChC,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA,WACI,0GACA;4CAEN,SAAS;gDACP,OAAO,IAAI,CAAC,KAAK,IAAI;gDACrB,IAAI,aAAkB,eAAe,OAAO,UAAU,GAAG,MAAM;oDAC7D;gDACF;4CACF;;8DAEA,6LAAC;oDAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,8BACA,WAAW,eAAe;;;;;;8DAE5B,6LAAC;oDAAK,WAAU;8DAAY,KAAK,KAAK;;;;;;;;;;;;wCAGvC,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAjCzD,KAAK,IAAI;;;;;4BAsCpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,6LAAC,qIAAA,CAAA,cAAW;gEACV,KAAK,MAAM;gEACX,KAAK,GAAG,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM,YAAY,IAAI;;;;;;0EAE7D,6LAAC,qIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,QAAQ,YAAY,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ,IAAI;;;;;;;;;;;;kEAIjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;;oEACV,WAAW,SAAS;oEAAI,MAAM,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;oEAAO;oEAAE,MAAM,YAAY;;;;;;;0EAEvG,6LAAC;gEAAE,WAAU;0EACV,MAAM,QAAQ;;;;;;;;;;;;kEAInB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/E,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,OAAO,IAAI,CAAC,WAAW,oBAAoB;gDAC1D,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;0DAGR,6LAAC,+IAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;0DAEjC,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;GA/OwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACH,kJAAA,CAAA,UAAO;QACO,sHAAA,CAAA,aAAU;;;KAJ3B", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Menu, Bell, Search } from 'lucide-react';\nimport { useUIStore, useNotificationStore } from '@/lib/store';\n\ninterface HeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nexport default function Header({ title, subtitle }: HeaderProps) {\n  const { toggleSidebar } = useUIStore();\n  const { notifications, unreadCount, markAsRead } = useNotificationStore();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleNotificationClick = (index: number) => {\n    markAsRead(index);\n  };\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-4 py-3 lg:px-6\">\n      <div className=\"flex items-center justify-between\">\n        {/* Left side */}\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleSidebar}\n            className=\"lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Search - Hidden on mobile */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                <Bell className=\"w-5 h-5\" />\n                {unreadCount > 0 && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className=\"absolute -top-1 -right-1\"\n                  >\n                    <Badge \n                      variant=\"destructive\" \n                      className=\"h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                    >\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </Badge>\n                  </motion.div>\n                )}\n              </Button>\n            </DropdownMenuTrigger>\n            \n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <h3 className=\"font-semibold text-gray-900\">Notifications</h3>\n                {unreadCount > 0 && (\n                  <p className=\"text-sm text-gray-600\">\n                    You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"max-h-96 overflow-y-auto\">\n                {notifications.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    <Bell className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                    <p className=\"text-sm\">No notifications yet</p>\n                  </div>\n                ) : (\n                  notifications.slice(0, 10).map((notification, index) => (\n                    <DropdownMenuItem\n                      key={index}\n                      className=\"p-3 cursor-pointer hover:bg-gray-50\"\n                      onClick={() => handleNotificationClick(index)}\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(notification.timestamp).toLocaleString()}\n                        </p>\n                      </div>\n                      {index < unreadCount && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full ml-2\" />\n                      )}\n                    </DropdownMenuItem>\n                  ))\n                )}\n              </div>\n\n              {notifications.length > 10 && (\n                <div className=\"p-3 border-t border-gray-200 text-center\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-blue-600\">\n                    View all notifications\n                  </Button>\n                </div>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Mobile search button */}\n          <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n            <Search className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AACA;;;AAbA;;;;;;;;AAoBe,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,0BAA0B,CAAC;QAC/B,WAAW;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;gCACpD,0BACC,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;8CAOpC,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;gDAC3C,cAAc,mBACb,6LAAC;oDAAE,WAAU;;wDAAwB;wDACzB;wDAAY;wDAAqB,gBAAgB,IAAI,MAAM;;;;;;;;;;;;;sDAK3E,6LAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;uDAGzB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc,sBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,WAAU;oDACV,SAAS,IAAM,wBAAwB;;sEAEvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,6LAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;;;;;;;wDAGnD,QAAQ,6BACP,6LAAC;4DAAI,WAAU;;;;;;;mDAbZ;;;;;;;;;;wCAoBZ,cAAc,MAAM,GAAG,oBACtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCASpE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;sCAC1C,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA9HwB;;QACI,sHAAA,CAAA,aAAU;QACe,sHAAA,CAAA,uBAAoB;;;KAFjD", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle?: string;\n}\n\nexport default function DashboardLayout({\n  children,\n  title,\n  subtitle\n}: DashboardLayoutProps) {\n  const router = useRouter();\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const { sidebarOpen } = useUIStore();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n  }, [user, isLoading, isAuthenticated, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar />\n      \n      <div\n        className={cn(\n          'transition-all duration-300 ease-in-out',\n          sidebarOpen ? 'lg:ml-64' : 'lg:ml-0'\n        )}\n      >\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-4 lg:p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {children}\n          </motion.div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAiBe,SAAS,gBAAgB,EACtC,QAAQ,EACR,KAAK,EACL,QAAQ,EACa;;IACrB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,WAAW;YAEf,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;oCAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,cAAc,aAAa;;kCAG7B,6LAAC,yIAAA,CAAA,UAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1DwB;;QAKP,qIAAA,CAAA,YAAS;QACqB,kJAAA,CAAA,UAAO;QAC5B,sHAAA,CAAA,aAAU;;;KAPZ", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/doctor/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { toast } from 'sonner';\nimport { Loader2, Save, User, MapPin, DollarSign, Stethoscope } from 'lucide-react';\n\ninterface DoctorProfile {\n  _id: string;\n  specialty: string;\n  bio: string;\n  experience: number;\n  consultationFee: number;\n  location: {\n    address: string;\n    city: string;\n    state: string;\n    zipCode: string;\n  };\n  qualifications: string[];\n  isVerified: boolean;\n  rating: number;\n  totalRatings: number;\n}\n\ninterface UserProfile {\n  firstName: string;\n  lastName: string;\n  phone: string;\n}\n\nconst specialties = [\n  'General Practice',\n  'Cardiology',\n  'Dermatology',\n  'Endocrinology',\n  'Gastroenterology',\n  'Neurology',\n  'Oncology',\n  'Orthopedics',\n  'Pediatrics',\n  'Psychiatry',\n  'Radiology',\n  'Surgery',\n  'Urology',\n  'Other'\n];\n\nexport default function DoctorProfilePage() {\n  const [doctorProfile, setDoctorProfile] = useState<DoctorProfile | null>(null);\n  const [userProfile, setUserProfile] = useState<UserProfile>({\n    firstName: '',\n    lastName: '',\n    phone: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const router = useRouter();\n  const { user, isLoading: authLoading, isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n\n    if (user.role !== 'doctor') {\n      router.push('/dashboard');\n      return;\n    }\n\n    fetchDoctorProfile();\n  }, [user, isAuthenticated, authLoading, router]);\n\n  const fetchDoctorProfile = async () => {\n    try {\n      const response = await fetch('/api/doctors/me');\n      const result = await response.json();\n\n      if (result.success) {\n        setDoctorProfile(result.data);\n        // Set user profile data\n        if (result.data.user) {\n          setUserProfile({\n            firstName: result.data.firstName || user?.firstName || '',\n            lastName: result.data.lastName || user?.lastName || '',\n            phone: result.data.phone || user?.phone || ''\n          });\n        }\n      } else {\n        toast.error('Failed to load profile');\n      }\n    } catch (error) {\n      console.error('Error fetching doctor profile:', error);\n      toast.error('Failed to load profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDoctorProfileChange = (field: keyof DoctorProfile, value: any) => {\n    if (!doctorProfile) return;\n    \n    setDoctorProfile({\n      ...doctorProfile,\n      [field]: value\n    });\n  };\n\n  const handleLocationChange = (field: keyof DoctorProfile['location'], value: string) => {\n    if (!doctorProfile) return;\n    \n    setDoctorProfile({\n      ...doctorProfile,\n      location: {\n        ...doctorProfile.location,\n        [field]: value\n      }\n    });\n  };\n\n  const handleUserProfileChange = (field: keyof UserProfile, value: string) => {\n    setUserProfile({\n      ...userProfile,\n      [field]: value\n    });\n  };\n\n  const handleSave = async () => {\n    if (!doctorProfile) return;\n\n    setSaving(true);\n    try {\n      // Update doctor profile\n      const doctorResponse = await fetch('/api/doctors/me', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n        },\n        body: JSON.stringify({\n          specialty: doctorProfile.specialty,\n          bio: doctorProfile.bio,\n          experience: doctorProfile.experience,\n          consultationFee: doctorProfile.consultationFee,\n          location: doctorProfile.location,\n          qualifications: doctorProfile.qualifications\n        }),\n      });\n\n      const doctorResult = await doctorResponse.json();\n\n      if (doctorResult.success) {\n        toast.success('Profile updated successfully');\n        // Update local state with the returned data\n        setDoctorProfile(doctorResult.data);\n      } else {\n        toast.error(doctorResult.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      toast.error('Failed to update profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!doctorProfile) {\n    return (\n      <DashboardLayout title=\"Profile\" subtitle=\"Manage your professional information\">\n        <div className=\"text-center py-12\">\n          <p className=\"text-gray-600\">No profile found. Please contact support.</p>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Profile\" subtitle=\"Manage your professional information\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        {/* Personal Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <User className=\"w-5 h-5 mr-2\" />\n              Personal Information\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"firstName\">First Name</Label>\n                <Input\n                  id=\"firstName\"\n                  value={userProfile.firstName}\n                  onChange={(e) => handleUserProfileChange('firstName', e.target.value)}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"lastName\">Last Name</Label>\n                <Input\n                  id=\"lastName\"\n                  value={userProfile.lastName}\n                  onChange={(e) => handleUserProfileChange('lastName', e.target.value)}\n                />\n              </div>\n            </div>\n            <div>\n              <Label htmlFor=\"phone\">Phone Number</Label>\n              <Input\n                id=\"phone\"\n                value={userProfile.phone}\n                onChange={(e) => handleUserProfileChange('phone', e.target.value)}\n                placeholder=\"+****************\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Professional Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Stethoscope className=\"w-5 h-5 mr-2\" />\n              Professional Information\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"specialty\">Specialty</Label>\n                <Select\n                  value={doctorProfile.specialty}\n                  onValueChange={(value) => handleDoctorProfileChange('specialty', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select specialty\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {specialties.map((specialty) => (\n                      <SelectItem key={specialty} value={specialty}>\n                        {specialty}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"experience\">Years of Experience</Label>\n                <Input\n                  id=\"experience\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={doctorProfile.experience}\n                  onChange={(e) => handleDoctorProfileChange('experience', parseInt(e.target.value) || 0)}\n                />\n              </div>\n            </div>\n            <div>\n              <Label htmlFor=\"consultationFee\">Consultation Fee ($)</Label>\n              <Input\n                id=\"consultationFee\"\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                value={doctorProfile.consultationFee}\n                onChange={(e) => handleDoctorProfileChange('consultationFee', parseFloat(e.target.value) || 0)}\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"bio\">Bio</Label>\n              <Textarea\n                id=\"bio\"\n                rows={4}\n                value={doctorProfile.bio}\n                onChange={(e) => handleDoctorProfileChange('bio', e.target.value)}\n                placeholder=\"Tell patients about yourself and your experience...\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Location Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <MapPin className=\"w-5 h-5 mr-2\" />\n              Practice Location\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"address\">Address</Label>\n              <Input\n                id=\"address\"\n                value={doctorProfile.location.address}\n                onChange={(e) => handleLocationChange('address', e.target.value)}\n                placeholder=\"123 Main Street\"\n              />\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"city\">City</Label>\n                <Input\n                  id=\"city\"\n                  value={doctorProfile.location.city}\n                  onChange={(e) => handleLocationChange('city', e.target.value)}\n                  placeholder=\"New York\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"state\">State</Label>\n                <Input\n                  id=\"state\"\n                  value={doctorProfile.location.state}\n                  onChange={(e) => handleLocationChange('state', e.target.value)}\n                  placeholder=\"NY\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"zipCode\">ZIP Code</Label>\n                <Input\n                  id=\"zipCode\"\n                  value={doctorProfile.location.zipCode}\n                  onChange={(e) => handleLocationChange('zipCode', e.target.value)}\n                  placeholder=\"10001\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Save Button */}\n        <div className=\"flex justify-end\">\n          <Button onClick={handleSave} disabled={saving} size=\"lg\">\n            {saving ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Saving...\n              </>\n            ) : (\n              <>\n                <Save className=\"w-4 h-4 mr-2\" />\n                Save Changes\n              </>\n            )}\n          </Button>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAuCA,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa;YAEjB,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC1B,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA;QACF;sCAAG;QAAC;QAAM;QAAiB;QAAa;KAAO;IAE/C,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI;gBAC5B,wBAAwB;gBACxB,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;oBACpB,eAAe;wBACb,WAAW,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM,aAAa;wBACvD,UAAU,OAAO,IAAI,CAAC,QAAQ,IAAI,MAAM,YAAY;wBACpD,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI,MAAM,SAAS;oBAC7C;gBACF;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B,CAAC,OAA4B;QAC7D,IAAI,CAAC,eAAe;QAEpB,iBAAiB;YACf,GAAG,aAAa;YAChB,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAwC;QACpE,IAAI,CAAC,eAAe;QAEpB,iBAAiB;YACf,GAAG,aAAa;YAChB,UAAU;gBACR,GAAG,cAAc,QAAQ;gBACzB,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,OAA0B;QACzD,eAAe;YACb,GAAG,WAAW;YACd,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,eAAe;QAEpB,UAAU;QACV,IAAI;YACF,wBAAwB;YACxB,MAAM,iBAAiB,MAAM,MAAM,mBAAmB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;gBAChE;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW,cAAc,SAAS;oBAClC,KAAK,cAAc,GAAG;oBACtB,YAAY,cAAc,UAAU;oBACpC,iBAAiB,cAAc,eAAe;oBAC9C,UAAU,cAAc,QAAQ;oBAChC,gBAAgB,cAAc,cAAc;gBAC9C;YACF;YAEA,MAAM,eAAe,MAAM,eAAe,IAAI;YAE9C,IAAI,aAAa,OAAO,EAAE;gBACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,4CAA4C;gBAC5C,iBAAiB,aAAa,IAAI;YACpC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,aAAa,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC,kJAAA,CAAA,UAAe;YAAC,OAAM;YAAU,UAAS;sBACxC,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;QAAC,OAAM;QAAU,UAAS;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIrC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,YAAY,SAAS;oDAC5B,UAAU,CAAC,IAAM,wBAAwB,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAGxE,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,YAAY,QAAQ;oDAC3B,UAAU,CAAC,IAAM,wBAAwB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAIzE,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,YAAY,KAAK;4CACxB,UAAU,CAAC,IAAM,wBAAwB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,mNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI5C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,cAAc,SAAS;oDAC9B,eAAe,CAAC,QAAU,0BAA0B,aAAa;;sEAEjE,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,YAAY,GAAG,CAAC,CAAC,0BAChB,6LAAC,qIAAA,CAAA,aAAU;oEAAiB,OAAO;8EAChC;mEADc;;;;;;;;;;;;;;;;;;;;;;sDAOzB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa;;;;;;8DAC5B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO,cAAc,UAAU;oDAC/B,UAAU,CAAC,IAAM,0BAA0B,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;8CAI3F,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,OAAO,cAAc,eAAe;4CACpC,UAAU,CAAC,IAAM,0BAA0B,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;8CAGhG,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAM;;;;;;sDACrB,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAM;4CACN,OAAO,cAAc,GAAG;4CACxB,UAAU,CAAC,IAAM,0BAA0B,OAAO,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIvC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,cAAc,QAAQ,CAAC,OAAO;4CACrC,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC/D,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,cAAc,QAAQ,CAAC,IAAI;oDAClC,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,cAAc,QAAQ,CAAC,KAAK;oDACnC,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,cAAc,QAAQ,CAAC,OAAO;oDACrC,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC/D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,UAAU;wBAAQ,MAAK;kCACjD,uBACC;;8CACE,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;8CACE,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GAzTwB;;QASP,qIAAA,CAAA,YAAS;QACkC,kJAAA,CAAA,UAAO;;;KAV3C", "debugId": null}}]}