'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Home,
  Calendar,
  Users,
  User,
  LogOut,
  Menu,
  X,
  ClipboardList,
  Search,
  Bell,
} from 'lucide-react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useUIStore } from '@/lib/store';
import { toast } from 'sonner';
import Logo from '@/components/ui/logo';

interface SidebarProps {
  className?: string;
}

export default function Sidebar({ className }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { sidebarOpen, toggleSidebar } = useUIStore();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const isDoctor = user?.role === 'doctor';

  const doctorNavItems = [
    {
      title: 'Dashboard',
      href: '/doctor/dashboard',
      icon: Home,
    },
    {
      title: 'Appointments',
      href: '/doctor/appointments',
      icon: Calendar,
    },
    {
      title: 'Profile',
      href: '/doctor/profile',
      icon: User,
    },
  ];

  const patientNavItems = [
    {
      title: 'Dashboard',
      href: '/patient/dashboard',
      icon: Home,
    },
    {
      title: 'Find Doctors',
      href: '/doctors',
      icon: Search,
    },
    {
      title: 'My Appointments',
      href: '/patient/appointments',
      icon: Calendar,
    },
    {
      title: 'Profile',
      href: '/patient/profile',
      icon: User,
    },
  ];

  const navItems = isDoctor ? doctorNavItems : patientNavItems;

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error) {
      toast.error('Failed to logout');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';
    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';
    return `${first}${last}`.toUpperCase();
  };

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
  };

  return (
    <>
      {/* Mobile overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        animate={sidebarOpen ? 'open' : 'closed'}
        className={cn(
          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-xl lg:relative lg:translate-x-0 lg:shadow-none',
          'flex flex-col',
          className
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <Logo size="md" variant="default" />

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="lg:hidden hover:bg-gray-100 transition-colors"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <motion.div
                  key={item.href}
                  whileHover={{ x: 2 }}
                  whileTap={{ scale: 0.98 }}
                  className="relative"
                >
                  <Button
                    variant={isActive ? 'default' : 'ghost'}
                    size="default"
                    className={cn(
                      'w-full justify-start text-left h-11 px-3 rounded-lg transition-all duration-200',
                      isActive
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-sm hover:from-blue-700 hover:to-blue-800'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    )}
                    onClick={() => {
                      router.push(item.href);
                      if (typeof window !== 'undefined' && window.innerWidth < 1024) {
                        toggleSidebar();
                      }
                    }}
                  >
                    <Icon className={cn(
                      'w-5 h-5 mr-3 flex-shrink-0',
                      isActive ? 'text-white' : 'text-gray-500'
                    )} />
                    <span className="truncate">{item.title}</span>
                  </Button>

                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-r-full"
                      initial={false}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    />
                  )}
                </motion.div>
              );
            })}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-gray-200 bg-gray-50/50">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-start p-3 h-auto rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200"
                >
                  <div className="flex items-center space-x-3 w-full">
                    <Avatar className="w-10 h-10 ring-2 ring-blue-100">
                      <AvatarImage
                        src={user?.profileImage}
                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}
                      />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 text-left min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {user?.role || 'User'}
                      </p>
                    </div>

                    <div className="text-gray-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align="end" className="w-56 shadow-lg border-0 bg-white">
                <DropdownMenuItem
                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}
                  className="cursor-pointer hover:bg-gray-50 transition-colors"
                >
                  <User className="mr-2 h-4 w-4 text-gray-500" />
                  <span>Profile</span>
                </DropdownMenuItem>

                <DropdownMenuSeparator className="bg-gray-100" />

                <DropdownMenuItem
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="cursor-pointer text-red-600 hover:bg-red-50 hover:text-red-700 focus:text-red-700 transition-colors"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </motion.aside>
    </>
  );
}
