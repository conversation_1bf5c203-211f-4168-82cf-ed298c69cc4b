/**
 * Utility function to safely generate initials from first and last names
 * Handles undefined, null, and empty string values gracefully
 */

export function getInitials(firstName?: string | null, lastName?: string | null): string {
  // Safely get first character of first name
  const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';
  
  // Safely get first character of last name
  const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';
  
  return `${first}${last}`.toUpperCase();
}

/**
 * Alternative function that returns a single initial if only one name is provided
 */
export function getSingleInitial(name?: string | null): string {
  return name && name.length > 0 ? name.charAt(0).toUpperCase() : 'U';
}

/**
 * Function to get initials with custom fallback
 */
export function getInitialsWithFallback(
  firstName?: string | null, 
  lastName?: string | null, 
  fallback: string = 'UU'
): string {
  if (!firstName && !lastName) {
    return fallback;
  }
  
  if (!firstName) {
    return getSingleInitial(lastName) + getSingleInitial(lastName);
  }
  
  if (!lastName) {
    return getSingleInitial(firstName) + getSingleInitial(firstName);
  }
  
  return getInitials(firstName, lastName);
}
