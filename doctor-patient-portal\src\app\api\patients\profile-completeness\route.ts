import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Patient from '@/models/Patient';
import { authenticateUser } from '@/lib/auth';
import { validatePatientProfileCompleteness } from '@/lib/profile-validation';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    // Fetch patient profile
    const patient = await Patient.findOne({ userId: user._id });
    
    // Validate profile completeness
    const completeness = validatePatientProfileCompleteness(patient);

    return NextResponse.json<ApiResponse>({
      success: true,
      data: completeness,
    });

  } catch (error) {
    console.error('Profile completeness check error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to check profile completeness',
    }, { status: 500 });
  }
}
