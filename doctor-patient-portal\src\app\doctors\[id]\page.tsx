'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { ArrowLeft, MapPin, Star, DollarSign, Clock, Calendar, Phone, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { DoctorWithUser } from '@/types';
import { useAuth } from '@/components/providers/AuthProvider';
import { toast } from 'sonner';

interface DoctorProfilePageProps {
  params: Promise<{ id: string }>;
}

export default function DoctorProfilePage({ params }: DoctorProfilePageProps) {
  const [doctor, setDoctor] = useState<DoctorWithUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const { id } = use(params);

  useEffect(() => {
    const fetchDoctor = async () => {
      try {
        const response = await fetch(`/api/doctors/${id}`);
        const result = await response.json();

        if (result.success) {
          console.log('Doctor data received:', result.data);
          setDoctor(result.data);
        } else {
          toast.error('Doctor not found');
          router.push('/doctors');
        }
      } catch (error) {
        toast.error('Failed to fetch doctor profile');
        router.push('/doctors');
      } finally {
        setLoading(false);
      }
    };

    fetchDoctor();
  }, [id, router]);

  const handleBookAppointment = () => {
    if (!isAuthenticated) {
      toast.error('Please login to book an appointment');
      router.push('/login');
      return;
    }

    if (user?.role !== 'patient') {
      toast.error('Only patients can book appointments');
      return;
    }

    router.push(`/patient/book/${id}`);
  };

  const getInitials = (firstName: string, lastName: string) => {
    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';
    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';
    return `${first}${last}`.toUpperCase();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-5 h-5 ${
          index < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!doctor) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Doctor not found</h2>
          <Button onClick={() => router.push('/doctors')}>
            Back to Doctors
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <div className="flex flex-col md:flex-row md:items-start md:space-x-6">
            <Avatar className="w-32 h-32 mx-auto md:mx-0 mb-4 md:mb-0">
              <AvatarImage
                src={doctor?.profileImage}
                alt={`Dr. ${doctor?.firstName || 'Doctor'} ${doctor?.lastName || ''}`}
              />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-3xl font-semibold">
                {doctor ? getInitials(doctor.firstName || '', doctor.lastName || '') : 'DR'}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Dr. {doctor?.firstName || 'Doctor'} {doctor?.lastName || ''}
              </h1>
              
              <Badge variant="secondary" className="mb-3 bg-blue-50 text-blue-700">
                {doctor.specialty}
              </Badge>

              <div className="flex items-center justify-center md:justify-start space-x-1 mb-4">
                {renderStars(doctor.rating)}
                <span className="text-lg font-medium text-gray-900 ml-2">
                  {doctor.rating.toFixed(1)}
                </span>
                <span className="text-gray-600">
                  ({doctor.totalRatings} reviews)
                </span>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0 text-gray-600">
                <div className="flex items-center justify-center md:justify-start">
                  <Clock className="w-5 h-5 mr-2" />
                  <span>{doctor.experience} years experience</span>
                </div>
                
                <div className="flex items-center justify-center md:justify-start">
                  <MapPin className="w-5 h-5 mr-2" />
                  <span>{doctor.location.city}, {doctor.location.state}</span>
                </div>
                
                <div className="flex items-center justify-center md:justify-start">
                  <DollarSign className="w-5 h-5 mr-2" />
                  <span>${doctor.consultationFee} consultation</span>
                </div>
              </div>
            </div>

            <div className="mt-6 md:mt-0">
              <Button
                onClick={handleBookAppointment}
                size="lg"
                className="w-full md:w-auto bg-blue-600 hover:bg-blue-700"
                disabled={!isAuthenticated || user?.role !== 'patient'}
              >
                <Calendar className="w-5 h-5 mr-2" />
                Book Appointment
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* About */}
            <Card>
              <CardHeader>
                <CardTitle>About Dr. {doctor?.lastName || 'Doctor'}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {doctor.bio}
                </p>
              </CardContent>
            </Card>

            {/* Specialization & Experience */}
            <Card>
              <CardHeader>
                <CardTitle>Professional Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Specialization</h4>
                  <Badge variant="outline" className="text-blue-700 border-blue-200">
                    {doctor.specialty}
                  </Badge>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Experience</h4>
                  <p className="text-gray-700">
                    {doctor.experience} years of professional medical practice
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-700">{doctor.user.email}</span>
                </div>
                
                {doctor.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{doctor.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Location */}
            <Card>
              <CardHeader>
                <CardTitle>Clinic Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-gray-700">{doctor.location.address}</p>
                  <p className="text-gray-700">
                    {doctor.location.city}, {doctor.location.state} {doctor.location.zipCode}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Consultation Fee */}
            <Card>
              <CardHeader>
                <CardTitle>Consultation Fee</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    ${doctor.consultationFee}
                  </div>
                  <p className="text-gray-600">per consultation</p>
                </div>
              </CardContent>
            </Card>

            {/* Book Appointment Button */}
            <Button
              onClick={handleBookAppointment}
              size="lg"
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              disabled={!isAuthenticated || user?.role !== 'patient'}
            >
              <Calendar className="w-5 h-5 mr-2" />
              {!isAuthenticated ? 'Login to Book' : user?.role !== 'patient' ? 'Patients Only' : 'Book Appointment'}
            </Button>

            {/* Show login prompt if not authenticated */}
            {!isAuthenticated && (
              <p className="text-sm text-gray-600 mt-2 text-center">
                Please <button onClick={() => router.push('/login')} className="text-blue-600 underline">login</button> to book an appointment
              </p>
            )}

            {/* Show role message if not patient */}
            {isAuthenticated && user?.role !== 'patient' && (
              <p className="text-sm text-orange-600 mt-2 text-center">
                Only patients can book appointments
              </p>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}
