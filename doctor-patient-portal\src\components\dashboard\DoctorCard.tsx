'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MapPin, Star, DollarSign, Clock, User } from 'lucide-react';
import { DoctorWithUser } from '@/types';
import { useAuthStore } from '@/lib/store';

interface DoctorCardProps {
  doctor: Doctor<PERSON>ithUser;
  showBookButton?: boolean;
}

export default function DoctorCard({ doctor, showBookButton = true }: DoctorCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();

  // Safe access to user profile data with fallbacks
  const userProfile = doctor.user?.profile;
  const firstName = userProfile?.firstName || 'Unknown';
  const lastName = userProfile?.lastName || 'Doctor';
  const profileImage = doctor.profileImage || userProfile?.profileImage;

  const handleBookAppointment = () => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user?.role !== 'patient') {
      return;
    }

    router.push(`/patient/book/${doctor._id}`);
  };

  const handleViewProfile = () => {
    router.push(`/doctors/${doctor._id}`);
  };

  const getInitials = (firstName: string, lastName: string) => {
    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';
    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';
    return `${first}${last}`.toUpperCase();
  };

  // Early return if doctor data is incomplete
  if (!doctor || !doctor.user) {
    return (
      <Card className="h-full">
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <p>Doctor information unavailable</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className="h-full transition-all duration-300 hover:shadow-lg border-0 shadow-md">
        <CardHeader className="pb-4">
          <div className="flex items-start space-x-4">
            <Avatar className="w-16 h-16">
              <AvatarImage
                src={profileImage}
                alt={`Dr. ${firstName} ${lastName}`}
              />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-lg font-semibold">
                {getInitials(firstName || '', lastName || '')}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                Dr. {firstName} {lastName}
              </h3>
              
              <Badge variant="secondary" className="mt-1 bg-blue-50 text-blue-700 hover:bg-blue-100">
                {doctor.specialty}
              </Badge>
              
              <div className="flex items-center mt-2 space-x-1">
                {renderStars(doctor.rating)}
                <span className="text-sm text-gray-600 ml-2">
                  {doctor.rating.toFixed(1)} ({doctor.totalRatings} reviews)
                </span>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pb-4">
          <p className="text-gray-600 text-sm line-clamp-3 mb-4">
            {doctor.bio}
          </p>

          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-2 text-gray-400" />
              <span>{doctor.experience} years experience</span>
            </div>

            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="w-4 h-4 mr-2 text-gray-400" />
              <span className="truncate">
                {doctor.location.city}, {doctor.location.state}
              </span>
            </div>

            <div className="flex items-center text-sm text-gray-600">
              <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
              <span>${doctor.consultationFee} consultation fee</span>
            </div>
          </div>
        </CardContent>

        <CardFooter className="pt-0 space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewProfile}
            className="flex-1"
          >
            <User className="w-4 h-4 mr-2" />
            View Profile
          </Button>

          {showBookButton && (
            <motion.div
              className="flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={handleBookAppointment}
                size="sm"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!isAuthenticated || user?.role !== 'patient'}
              >
                Book Appointment
              </Button>
            </motion.div>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
}
