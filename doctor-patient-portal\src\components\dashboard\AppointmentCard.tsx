'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Calendar, 
  Clock, 
  User, 
  MapPin, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AppointmentWithDetails, AppointmentStatus } from '@/types';
import { useAuthStore } from '@/lib/store';
import { toast } from 'sonner';

interface AppointmentCardProps {
  appointment: AppointmentWithDetails;
  onStatusUpdate?: (appointmentId: string, status: AppointmentStatus) => void;
  onViewDetails?: (appointment: AppointmentWithDetails) => void;
}

export default function AppointmentCard({ 
  appointment, 
  onStatusUpdate, 
  onViewDetails 
}: AppointmentCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthStore();

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: AppointmentStatus) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleStatusUpdate = async (newStatus: AppointmentStatus) => {
    if (!onStatusUpdate) return;

    setIsLoading(true);
    try {
      await onStatusUpdate(appointment._id, newStatus);
      toast.success(`Appointment ${newStatus} successfully`);
    } catch (error) {
      toast.error('Failed to update appointment status');
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';
    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';
    return `${first}${last}`.toUpperCase();
  };

  const isDoctor = user?.role === 'doctor';
  const canUpdateStatus = isDoctor && ['pending'].includes(appointment.status);
  const displayUser = isDoctor ? appointment.patient : appointment.doctor;
  const displayName = isDoctor
    ? `${appointment.patient.firstName} ${appointment.patient.lastName}`
    : `Dr. ${appointment.doctor.firstName} ${appointment.doctor.lastName}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -2 }}
    >
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <Avatar className="w-12 h-12">
                <AvatarImage
                  src={displayUser.profileImage}
                  alt={displayName}
                />
                <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                  {getInitials(displayUser.firstName || '', displayUser.lastName || '')}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 truncate">
                  {displayName}
                </h3>
                
                {!isDoctor && (
                  <p className="text-sm text-gray-600">
                    {appointment.doctor.specialty}
                  </p>
                )}
                
                <div className="flex items-center mt-1">
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(appointment.status)} border`}
                  >
                    {getStatusIcon(appointment.status)}
                    <span className="ml-1 capitalize">{appointment.status}</span>
                  </Badge>
                </div>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onViewDetails?.(appointment)}>
                  <FileText className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                
                {canUpdateStatus && (
                  <>
                    <DropdownMenuItem 
                      onClick={() => handleStatusUpdate('approved')}
                      disabled={isLoading}
                    >
                      <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                      Approve
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleStatusUpdate('rejected')}
                      disabled={isLoading}
                    >
                      <XCircle className="mr-2 h-4 w-4 text-red-600" />
                      Reject
                    </DropdownMenuItem>
                  </>
                )}
                
                {appointment.status === 'pending' && !isDoctor && (
                  <DropdownMenuItem 
                    onClick={() => handleStatusUpdate('cancelled')}
                    disabled={isLoading}
                  >
                    <XCircle className="mr-2 h-4 w-4 text-red-600" />
                    Cancel
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="w-4 h-4 mr-2 text-gray-400" />
              <span>{format(new Date(appointment.dateTime), 'MMMM d, yyyy')}</span>
            </div>

            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-2 text-gray-400" />
              <span>{format(new Date(appointment.dateTime), 'h:mm a')}</span>
            </div>

            {!isDoctor && (
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                <span className="truncate">
                  {appointment.doctor.location.city}, {appointment.doctor.location.state}
                </span>
              </div>
            )}

            {appointment.symptoms && (
              <div className="mt-3 p-2 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Symptoms: </span>
                  {appointment.symptoms}
                </p>
              </div>
            )}

            {appointment.notes && (
              <div className="mt-2 p-2 bg-blue-50 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Notes: </span>
                  {appointment.notes}
                </p>
              </div>
            )}

            {appointment.diagnosis && (
              <div className="mt-2 p-2 bg-green-50 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Diagnosis: </span>
                  {appointment.diagnosis}
                </p>
              </div>
            )}

            {appointment.prescription && (
              <div className="mt-2 p-2 bg-purple-50 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Prescription: </span>
                  {appointment.prescription}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
