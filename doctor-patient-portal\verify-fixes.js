const http = require('http');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function verifyFixes() {
  console.log('🔍 Verifying Patient Profile and Appointment Booking Fixes...\n');

  const baseUrl = 'http://localhost:3000';
  let authToken = null;

  try {
    // Step 1: Register or login a test patient
    console.log('1️⃣ Setting up test patient...');
    
    const testPatient = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'Patient',
      role: 'patient'
    };

    // Try to register
    const registerResponse = await makeRequest(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testPatient)
    });

    if (registerResponse.status === 200 && registerResponse.data.success) {
      authToken = registerResponse.data.data.token;
      console.log('✅ Test patient registered successfully');
    } else {
      // Try to login if already exists
      const loginResponse = await makeRequest(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testPatient.email,
          password: testPatient.password
        })
      });

      if (loginResponse.status === 200 && loginResponse.data.success) {
        authToken = loginResponse.data.data.token;
        console.log('✅ Test patient logged in successfully');
      } else {
        throw new Error('Failed to setup test patient');
      }
    }

    // Step 2: Test profile completeness check with incomplete profile
    console.log('\n2️⃣ Testing profile completeness with incomplete profile...');
    
    const completenessResponse = await makeRequest(`${baseUrl}/api/patients/profile-completeness`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    if (completenessResponse.status === 200 && completenessResponse.data.success) {
      const completeness = completenessResponse.data.data;
      console.log('✅ Profile completeness check working');
      console.log(`   Complete: ${completeness.isComplete}`);
      console.log(`   Completion: ${completeness.completionPercentage}%`);
      console.log(`   Missing fields: ${completeness.missingFields.join(', ')}`);
    } else {
      console.log('❌ Profile completeness check failed');
    }

    // Step 3: Test appointment booking with incomplete profile
    console.log('\n3️⃣ Testing appointment booking with incomplete profile...');
    
    const appointmentData = {
      doctorId: '507f1f77bcf86cd799439011', // Dummy doctor ID
      dateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      symptoms: 'Test symptoms',
      notes: 'Test notes'
    };

    const bookingResponse = await makeRequest(`${baseUrl}/api/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(appointmentData)
    });

    if (bookingResponse.status === 400 && !bookingResponse.data.success) {
      console.log('✅ Appointment booking correctly blocked for incomplete profile');
      console.log(`   Error: ${bookingResponse.data.error}`);
      if (bookingResponse.data.data?.missingFields) {
        console.log(`   Missing fields: ${bookingResponse.data.data.missingFields.join(', ')}`);
      }
    } else {
      console.log('❌ Appointment booking should have been blocked');
    }

    // Step 4: Test profile update
    console.log('\n4️⃣ Testing profile update...');
    
    const profileUpdateData = {
      firstName: 'Updated',
      lastName: 'Patient',
      phone: '******-123-4567',
      dateOfBirth: '1990-01-15',
      gender: 'male',
      address: '123 Test Street',
      city: 'Test City',
      state: 'TS',
      zipCode: '12345',
      emergencyContact: {
        name: 'Emergency Contact',
        relationship: 'Spouse',
        phone: '******-987-6543'
      },
      medicalHistory: {
        allergies: 'None',
        medications: 'None',
        conditions: 'None',
        surgeries: 'None'
      },
      insurance: {
        provider: 'Test Insurance',
        policyNumber: 'POL123456',
        groupNumber: 'GRP789'
      }
    };

    const updateResponse = await makeRequest(`${baseUrl}/api/patients/me`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(profileUpdateData)
    });

    if (updateResponse.status === 200 && updateResponse.data.success) {
      console.log('✅ Profile update successful');
      console.log(`   Updated name: ${updateResponse.data.data.firstName} ${updateResponse.data.data.lastName}`);
      console.log(`   Phone: ${updateResponse.data.data.phone}`);
      console.log(`   DOB: ${updateResponse.data.data.dateOfBirth}`);
    } else {
      console.log('❌ Profile update failed');
      console.log(`   Error: ${updateResponse.data.error}`);
    }

    // Step 5: Test profile completeness after update
    console.log('\n5️⃣ Testing profile completeness after update...');
    
    const completenessAfterResponse = await makeRequest(`${baseUrl}/api/patients/profile-completeness`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    if (completenessAfterResponse.status === 200 && completenessAfterResponse.data.success) {
      const completeness = completenessAfterResponse.data.data;
      console.log('✅ Profile completeness check after update');
      console.log(`   Complete: ${completeness.isComplete}`);
      console.log(`   Completion: ${completeness.completionPercentage}%`);
      if (completeness.missingFields.length > 0) {
        console.log(`   Missing fields: ${completeness.missingFields.join(', ')}`);
      } else {
        console.log('   All required fields completed!');
      }
    } else {
      console.log('❌ Profile completeness check failed after update');
    }

    // Step 6: Test appointment booking with complete profile
    console.log('\n6️⃣ Testing appointment booking with complete profile...');
    
    // Note: This will still fail because we're using a dummy doctor ID
    // But it should fail with a different error (doctor not found) rather than incomplete profile
    const bookingAfterResponse = await makeRequest(`${baseUrl}/api/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(appointmentData)
    });

    if (bookingAfterResponse.status === 404 && bookingAfterResponse.data.error === 'Doctor not found') {
      console.log('✅ Profile completeness validation passed (failed on doctor not found as expected)');
    } else if (bookingAfterResponse.status === 400 && bookingAfterResponse.data.error.includes('complete your profile')) {
      console.log('❌ Still failing on profile completeness');
      console.log(`   Error: ${bookingAfterResponse.data.error}`);
    } else {
      console.log(`ℹ️  Booking response: ${bookingAfterResponse.status} - ${bookingAfterResponse.data.error}`);
    }

    console.log('\n🎉 Fix verification completed!');

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

// Run the verification
verifyFixes().catch(console.error);
