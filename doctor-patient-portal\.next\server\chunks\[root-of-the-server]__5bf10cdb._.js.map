{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Patient.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Patient as IPatient } from '@/types';\n\nexport interface PatientDocument extends Document, Omit<IPatient, '_id'> {}\n\nconst EmergencyContactSchema = new Schema({\n  name: {\n    type: String,\n    trim: true,\n  },\n  relationship: {\n    type: String,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst MedicalHistorySchema = new Schema({\n  allergies: {\n    type: String,\n    trim: true,\n  },\n  medications: {\n    type: String,\n    trim: true,\n  },\n  conditions: {\n    type: String,\n    trim: true,\n  },\n  surgeries: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst InsuranceSchema = new Schema({\n  provider: {\n    type: String,\n    trim: true,\n  },\n  policyNumber: {\n    type: String,\n    trim: true,\n  },\n  groupNumber: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst PatientSchema = new Schema<PatientDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other', 'prefer-not-to-say'],\n  },\n  address: {\n    type: String,\n    trim: true,\n  },\n  city: {\n    type: String,\n    trim: true,\n  },\n  state: {\n    type: String,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    trim: true,\n  },\n  emergencyContact: {\n    type: EmergencyContactSchema,\n  },\n  medicalHistory: {\n    type: MedicalHistorySchema,\n  },\n  insurance: {\n    type: InsuranceSchema,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nPatientSchema.index({ firstName: 1, lastName: 1 });\nPatientSchema.index({ city: 1 });\nPatientSchema.index({ state: 1 });\n\nexport default mongoose.models.Patient || mongoose.model<PatientDocument>('Patient', PatientSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACxC,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACjC,UAAU;QACR,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAkB;IAChD,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;YAAS;SAAoB;IACxD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,cAAc,KAAK,CAAC;IAAE,WAAW;IAAG,UAAU;AAAE;AAChD,cAAc,KAAK,CAAC;IAAE,MAAM;AAAE;AAC9B,cAAc,KAAK,CAAC;IAAE,OAAO;AAAE;uCAEhB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAkB,WAAW", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface UserDocument extends Document {\n  email: string;\n  password: string;\n  role: 'doctor' | 'patient';\n  isEmailVerified: boolean;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\n\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport User from '@/models/User';\n\nexport async function authenticateUser(request: NextRequest) {\n  const authHeader = request.headers.get('Authorization');\n  const cookieToken = request.cookies.get('authToken')?.value;\n\n  const token = authHeader?.replace('Bearer ', '') || cookieToken;\n\n  if (!token) {\n    throw new Error('No authentication token provided');\n  }\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n    const user = await User.findById(decoded.userId);\n\n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Invalid or expired token');\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,eAAe,iBAAiB,OAAoB;IACzD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IAEtD,MAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;IAEpD,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAE/C,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/patients/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport User from '@/models/User';\nimport Patient from '@/models/Patient';\nimport { authenticateUser } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if user is a patient\n    if (user.role !== 'patient') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Access denied. Only patients can access this endpoint.',\n      }, { status: 403 });\n    }\n\n    // Fetch patient profile data\n    const patient = await Patient.findOne({ userId: user._id });\n\n    if (!patient) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Patient profile not found',\n      }, { status: 404 });\n    }\n\n    // Return patient profile data\n    const patientData = {\n      id: patient._id,\n      userId: user._id,\n      email: user.email,\n      firstName: patient.firstName,\n      lastName: patient.lastName,\n      phone: patient.phone,\n      dateOfBirth: patient.dateOfBirth,\n      gender: patient.gender,\n      address: patient.address,\n      city: patient.city,\n      state: patient.state,\n      zipCode: patient.zipCode,\n      emergencyContact: patient.emergencyContact,\n      medicalHistory: patient.medicalHistory,\n      insurance: patient.insurance,\n    };\n\n    return NextResponse.json<ApiResponse>({\n      success: true,\n      data: patientData,\n    });\n\n  } catch (error) {\n    console.error('Get patient profile error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to fetch patient profile',\n    }, { status: 500 });\n  }\n}\n\nexport async function PUT(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if user is a patient\n    if (user.role !== 'patient') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Access denied. Only patients can access this endpoint.',\n      }, { status: 403 });\n    }\n\n    const body = await request.json();\n    console.log('📝 Patient profile update request body:', JSON.stringify(body, null, 2));\n\n    const {\n      firstName,\n      lastName,\n      phone,\n      dateOfBirth,\n      gender,\n      address,\n      city,\n      state,\n      zipCode,\n      emergencyContact,\n      medicalHistory,\n      insurance\n    } = body;\n\n    // Convert dateOfBirth string to Date object if provided\n    let parsedDateOfBirth = null;\n    if (dateOfBirth) {\n      parsedDateOfBirth = new Date(dateOfBirth);\n      // Validate the date\n      if (isNaN(parsedDateOfBirth.getTime())) {\n        return NextResponse.json<ApiResponse>({\n          success: false,\n          error: 'Invalid date format for dateOfBirth',\n        }, { status: 400 });\n      }\n    }\n\n    // Prepare update data, only including fields that are provided and not empty\n    const updateData: any = {};\n    if (firstName !== undefined && firstName.trim() !== '') updateData.firstName = firstName.trim();\n    if (lastName !== undefined && lastName.trim() !== '') updateData.lastName = lastName.trim();\n    if (phone !== undefined && phone.trim() !== '') updateData.phone = phone.trim();\n    if (parsedDateOfBirth !== null) updateData.dateOfBirth = parsedDateOfBirth;\n    if (gender !== undefined && gender.trim() !== '') updateData.gender = gender.trim();\n    if (address !== undefined && address.trim() !== '') updateData.address = address.trim();\n    if (city !== undefined && city.trim() !== '') updateData.city = city.trim();\n    if (state !== undefined && state.trim() !== '') updateData.state = state.trim();\n    if (zipCode !== undefined && zipCode.trim() !== '') updateData.zipCode = zipCode.trim();\n    // Handle nested objects - only include if they have meaningful data\n    if (emergencyContact !== undefined) {\n      const cleanedEmergencyContact = {\n        name: emergencyContact.name?.trim() || '',\n        relationship: emergencyContact.relationship?.trim() || '',\n        phone: emergencyContact.phone?.trim() || ''\n      };\n      updateData.emergencyContact = cleanedEmergencyContact;\n    }\n\n    if (medicalHistory !== undefined) {\n      const cleanedMedicalHistory = {\n        allergies: medicalHistory.allergies?.trim() || '',\n        medications: medicalHistory.medications?.trim() || '',\n        conditions: medicalHistory.conditions?.trim() || '',\n        surgeries: medicalHistory.surgeries?.trim() || ''\n      };\n      updateData.medicalHistory = cleanedMedicalHistory;\n    }\n\n    if (insurance !== undefined) {\n      const cleanedInsurance = {\n        provider: insurance.provider?.trim() || '',\n        policyNumber: insurance.policyNumber?.trim() || '',\n        groupNumber: insurance.groupNumber?.trim() || ''\n      };\n      updateData.insurance = cleanedInsurance;\n    }\n\n    console.log('📝 Update data being sent to database:', JSON.stringify(updateData, null, 2));\n\n    // Update patient profile\n    const updatedPatient = await Patient.findOneAndUpdate(\n      { userId: user._id },\n      { $set: updateData },\n      { new: true, runValidators: true, upsert: true }\n    );\n\n    if (!updatedPatient) {\n      console.error('❌ Failed to update patient profile - updatedPatient is null');\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Failed to update patient profile',\n      }, { status: 500 });\n    }\n\n    console.log('✅ Patient profile updated successfully:', updatedPatient._id);\n\n    const patientData = {\n      id: updatedPatient._id,\n      userId: user._id,\n      email: user.email,\n      firstName: updatedPatient.firstName,\n      lastName: updatedPatient.lastName,\n      phone: updatedPatient.phone,\n      dateOfBirth: updatedPatient.dateOfBirth,\n      gender: updatedPatient.gender,\n      address: updatedPatient.address,\n      city: updatedPatient.city,\n      state: updatedPatient.state,\n      zipCode: updatedPatient.zipCode,\n      emergencyContact: updatedPatient.emergencyContact,\n      medicalHistory: updatedPatient.medicalHistory,\n      insurance: updatedPatient.insurance,\n    };\n\n    return NextResponse.json<ApiResponse>({\n      success: true,\n      data: patientData,\n      message: 'Patient profile updated successfully',\n    });\n\n  } catch (error) {\n    console.error('Update patient profile error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to update patient profile',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,MAAM,UAAU,MAAM,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAAE,QAAQ,KAAK,GAAG;QAAC;QAEzD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,8BAA8B;QAC9B,MAAM,cAAc;YAClB,IAAI,QAAQ,GAAG;YACf,QAAQ,KAAK,GAAG;YAChB,OAAO,KAAK,KAAK;YACjB,WAAW,QAAQ,SAAS;YAC5B,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,QAAQ,QAAQ,MAAM;YACtB,SAAS,QAAQ,OAAO;YACxB,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK;YACpB,SAAS,QAAQ,OAAO;YACxB,kBAAkB,QAAQ,gBAAgB;YAC1C,gBAAgB,QAAQ,cAAc;YACtC,WAAW,QAAQ,SAAS;QAC9B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,2CAA2C,KAAK,SAAS,CAAC,MAAM,MAAM;QAElF,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,MAAM,EACN,OAAO,EACP,IAAI,EACJ,KAAK,EACL,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,SAAS,EACV,GAAG;QAEJ,wDAAwD;QACxD,IAAI,oBAAoB;QACxB,IAAI,aAAa;YACf,oBAAoB,IAAI,KAAK;YAC7B,oBAAoB;YACpB,IAAI,MAAM,kBAAkB,OAAO,KAAK;gBACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;oBACpC,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,6EAA6E;QAC7E,MAAM,aAAkB,CAAC;QACzB,IAAI,cAAc,aAAa,UAAU,IAAI,OAAO,IAAI,WAAW,SAAS,GAAG,UAAU,IAAI;QAC7F,IAAI,aAAa,aAAa,SAAS,IAAI,OAAO,IAAI,WAAW,QAAQ,GAAG,SAAS,IAAI;QACzF,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO,IAAI,WAAW,KAAK,GAAG,MAAM,IAAI;QAC7E,IAAI,sBAAsB,MAAM,WAAW,WAAW,GAAG;QACzD,IAAI,WAAW,aAAa,OAAO,IAAI,OAAO,IAAI,WAAW,MAAM,GAAG,OAAO,IAAI;QACjF,IAAI,YAAY,aAAa,QAAQ,IAAI,OAAO,IAAI,WAAW,OAAO,GAAG,QAAQ,IAAI;QACrF,IAAI,SAAS,aAAa,KAAK,IAAI,OAAO,IAAI,WAAW,IAAI,GAAG,KAAK,IAAI;QACzE,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO,IAAI,WAAW,KAAK,GAAG,MAAM,IAAI;QAC7E,IAAI,YAAY,aAAa,QAAQ,IAAI,OAAO,IAAI,WAAW,OAAO,GAAG,QAAQ,IAAI;QACrF,oEAAoE;QACpE,IAAI,qBAAqB,WAAW;YAClC,MAAM,0BAA0B;gBAC9B,MAAM,iBAAiB,IAAI,EAAE,UAAU;gBACvC,cAAc,iBAAiB,YAAY,EAAE,UAAU;gBACvD,OAAO,iBAAiB,KAAK,EAAE,UAAU;YAC3C;YACA,WAAW,gBAAgB,GAAG;QAChC;QAEA,IAAI,mBAAmB,WAAW;YAChC,MAAM,wBAAwB;gBAC5B,WAAW,eAAe,SAAS,EAAE,UAAU;gBAC/C,aAAa,eAAe,WAAW,EAAE,UAAU;gBACnD,YAAY,eAAe,UAAU,EAAE,UAAU;gBACjD,WAAW,eAAe,SAAS,EAAE,UAAU;YACjD;YACA,WAAW,cAAc,GAAG;QAC9B;QAEA,IAAI,cAAc,WAAW;YAC3B,MAAM,mBAAmB;gBACvB,UAAU,UAAU,QAAQ,EAAE,UAAU;gBACxC,cAAc,UAAU,YAAY,EAAE,UAAU;gBAChD,aAAa,UAAU,WAAW,EAAE,UAAU;YAChD;YACA,WAAW,SAAS,GAAG;QACzB;QAEA,QAAQ,GAAG,CAAC,0CAA0C,KAAK,SAAS,CAAC,YAAY,MAAM;QAEvF,yBAAyB;QACzB,MAAM,iBAAiB,MAAM,0HAAA,CAAA,UAAO,CAAC,gBAAgB,CACnD;YAAE,QAAQ,KAAK,GAAG;QAAC,GACnB;YAAE,MAAM;QAAW,GACnB;YAAE,KAAK;YAAM,eAAe;YAAM,QAAQ;QAAK;QAGjD,IAAI,CAAC,gBAAgB;YACnB,QAAQ,KAAK,CAAC;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,2CAA2C,eAAe,GAAG;QAEzE,MAAM,cAAc;YAClB,IAAI,eAAe,GAAG;YACtB,QAAQ,KAAK,GAAG;YAChB,OAAO,KAAK,KAAK;YACjB,WAAW,eAAe,SAAS;YACnC,UAAU,eAAe,QAAQ;YACjC,OAAO,eAAe,KAAK;YAC3B,aAAa,eAAe,WAAW;YACvC,QAAQ,eAAe,MAAM;YAC7B,SAAS,eAAe,OAAO;YAC/B,MAAM,eAAe,IAAI;YACzB,OAAO,eAAe,KAAK;YAC3B,SAAS,eAAe,OAAO;YAC/B,kBAAkB,eAAe,gBAAgB;YACjD,gBAAgB,eAAe,cAAc;YAC7C,WAAW,eAAe,SAAS;QACrC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}