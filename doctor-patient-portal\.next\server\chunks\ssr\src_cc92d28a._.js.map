{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/doctors/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, use } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, MapPin, Star, DollarSign, Clock, Calendar, Phone, Mail } from 'lucide-react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Separator } from '@/components/ui/separator';\nimport { DoctorWithUser } from '@/types';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { toast } from 'sonner';\n\ninterface DoctorProfilePageProps {\n  params: Promise<{ id: string }>;\n}\n\nexport default function DoctorProfilePage({ params }: DoctorProfilePageProps) {\n  const [doctor, setDoctor] = useState<DoctorWithUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n  const { isAuthenticated, user } = useAuth();\n  const { id } = use(params);\n\n  useEffect(() => {\n    const fetchDoctor = async () => {\n      try {\n        const response = await fetch(`/api/doctors/${id}`);\n        const result = await response.json();\n\n        if (result.success) {\n          console.log('Doctor data received:', result.data);\n          setDoctor(result.data);\n        } else {\n          toast.error('Doctor not found');\n          router.push('/doctors');\n        }\n      } catch (error) {\n        toast.error('Failed to fetch doctor profile');\n        router.push('/doctors');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDoctor();\n  }, [id, router]);\n\n  const handleBookAppointment = () => {\n    if (!isAuthenticated) {\n      toast.error('Please login to book an appointment');\n      router.push('/login');\n      return;\n    }\n\n    if (user?.role !== 'patient') {\n      toast.error('Only patients can book appointments');\n      return;\n    }\n\n    router.push(`/patient/book/${id}`);\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    const first = firstName && firstName.length > 0 ? firstName.charAt(0) : 'U';\n    const last = lastName && lastName.length > 0 ? lastName.charAt(0) : 'U';\n    return `${first}${last}`.toUpperCase();\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <Star\n        key={index}\n        className={`w-5 h-5 ${\n          index < Math.floor(rating)\n            ? 'text-yellow-400 fill-current'\n            : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!doctor) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Doctor not found</h2>\n          <Button onClick={() => router.push('/doctors')}>\n            Back to Doctors\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            onClick={() => router.back()}\n            className=\"mb-4\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Back\n          </Button>\n\n          <div className=\"flex flex-col md:flex-row md:items-start md:space-x-6\">\n            <Avatar className=\"w-32 h-32 mx-auto md:mx-0 mb-4 md:mb-0\">\n              <AvatarImage\n                src={doctor?.profileImage}\n                alt={`Dr. ${doctor?.firstName || 'Doctor'} ${doctor?.lastName || ''}`}\n              />\n              <AvatarFallback className=\"bg-blue-100 text-blue-600 text-3xl font-semibold\">\n                {doctor ? getInitials(doctor.firstName || '', doctor.lastName || '') : 'DR'}\n              </AvatarFallback>\n            </Avatar>\n\n            <div className=\"flex-1 text-center md:text-left\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Dr. {doctor?.firstName || 'Doctor'} {doctor?.lastName || ''}\n              </h1>\n              \n              <Badge variant=\"secondary\" className=\"mb-3 bg-blue-50 text-blue-700\">\n                {doctor.specialty}\n              </Badge>\n\n              <div className=\"flex items-center justify-center md:justify-start space-x-1 mb-4\">\n                {renderStars(doctor.rating)}\n                <span className=\"text-lg font-medium text-gray-900 ml-2\">\n                  {doctor.rating.toFixed(1)}\n                </span>\n                <span className=\"text-gray-600\">\n                  ({doctor.totalRatings} reviews)\n                </span>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0 text-gray-600\">\n                <div className=\"flex items-center justify-center md:justify-start\">\n                  <Clock className=\"w-5 h-5 mr-2\" />\n                  <span>{doctor.experience} years experience</span>\n                </div>\n                \n                <div className=\"flex items-center justify-center md:justify-start\">\n                  <MapPin className=\"w-5 h-5 mr-2\" />\n                  <span>{doctor.location.city}, {doctor.location.state}</span>\n                </div>\n                \n                <div className=\"flex items-center justify-center md:justify-start\">\n                  <DollarSign className=\"w-5 h-5 mr-2\" />\n                  <span>${doctor.consultationFee} consultation</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mt-6 md:mt-0\">\n              <Button\n                onClick={handleBookAppointment}\n                size=\"lg\"\n                className=\"w-full md:w-auto bg-blue-600 hover:bg-blue-700\"\n                disabled={!isAuthenticated || user?.role !== 'patient'}\n              >\n                <Calendar className=\"w-5 h-5 mr-2\" />\n                Book Appointment\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* About */}\n            <Card>\n              <CardHeader>\n                <CardTitle>About Dr. {doctor?.lastName || 'Doctor'}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  {doctor.bio}\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Specialization & Experience */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Professional Information</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">Specialization</h4>\n                  <Badge variant=\"outline\" className=\"text-blue-700 border-blue-200\">\n                    {doctor.specialty}\n                  </Badge>\n                </div>\n                \n                <Separator />\n                \n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">Experience</h4>\n                  <p className=\"text-gray-700\">\n                    {doctor.experience} years of professional medical practice\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Contact Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Contact Information</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <Mail className=\"w-5 h-5 text-gray-400\" />\n                  <span className=\"text-gray-700\">{doctor.user.email}</span>\n                </div>\n                \n                {doctor.phone && (\n                  <div className=\"flex items-center space-x-3\">\n                    <Phone className=\"w-5 h-5 text-gray-400\" />\n                    <span className=\"text-gray-700\">{doctor.phone}</span>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Location */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Clinic Location</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  <p className=\"text-gray-700\">{doctor.location.address}</p>\n                  <p className=\"text-gray-700\">\n                    {doctor.location.city}, {doctor.location.state} {doctor.location.zipCode}\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Consultation Fee */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Consultation Fee</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                    ${doctor.consultationFee}\n                  </div>\n                  <p className=\"text-gray-600\">per consultation</p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Book Appointment Button */}\n            <Button\n              onClick={handleBookAppointment}\n              size=\"lg\"\n              className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n              disabled={!isAuthenticated || user?.role !== 'patient'}\n            >\n              <Calendar className=\"w-5 h-5 mr-2\" />\n              {!isAuthenticated ? 'Login to Book' : user?.role !== 'patient' ? 'Patients Only' : 'Book Appointment'}\n            </Button>\n\n            {/* Show login prompt if not authenticated */}\n            {!isAuthenticated && (\n              <p className=\"text-sm text-gray-600 mt-2 text-center\">\n                Please <button onClick={() => router.push('/login')} className=\"text-blue-600 underline\">login</button> to book an appointment\n              </p>\n            )}\n\n            {/* Show role message if not patient */}\n            {isAuthenticated && user?.role !== 'patient' && (\n              <p className=\"text-sm text-orange-600 mt-2 text-center\">\n                Only patients can book appointments\n              </p>\n            )}\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAbA;;;;;;;;;;;;AAmBe,SAAS,kBAAkB,EAAE,MAAM,EAA0B;IAC1E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,IAAI;gBACjD,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,GAAG,CAAC,yBAAyB,OAAO,IAAI;oBAChD,UAAU,OAAO,IAAI;gBACvB,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAI;KAAO;IAEf,MAAM,wBAAwB;QAC5B,IAAI,CAAC,iBAAiB;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM,SAAS,WAAW;YAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI;IACnC;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,MAAM,QAAQ,aAAa,UAAU,MAAM,GAAG,IAAI,UAAU,MAAM,CAAC,KAAK;QACxE,MAAM,OAAO,YAAY,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,CAAC,KAAK;QACpE,OAAO,GAAG,QAAQ,MAAM,CAAC,WAAW;IACtC;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,8OAAC,kMAAA,CAAA,OAAI;gBAEH,WAAW,CAAC,QAAQ,EAClB,QAAQ,KAAK,KAAK,CAAC,UACf,iCACA,iBACJ;eALG;;;;;IAQX;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;kCAAa;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CACV,KAAK,QAAQ;4CACb,KAAK,CAAC,IAAI,EAAE,QAAQ,aAAa,SAAS,CAAC,EAAE,QAAQ,YAAY,IAAI;;;;;;sDAEvE,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,SAAS,YAAY,OAAO,SAAS,IAAI,IAAI,OAAO,QAAQ,IAAI,MAAM;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAwC;gDAC/C,QAAQ,aAAa;gDAAS;gDAAE,QAAQ,YAAY;;;;;;;sDAG3D,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,OAAO,SAAS;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,OAAO,MAAM;8DAC1B,8OAAC;oDAAK,WAAU;8DACb,OAAO,MAAM,CAAC,OAAO,CAAC;;;;;;8DAEzB,8OAAC;oDAAK,WAAU;;wDAAgB;wDAC5B,OAAO,YAAY;wDAAC;;;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAM,OAAO,UAAU;gEAAC;;;;;;;;;;;;;8DAG3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;;gEAAM,OAAO,QAAQ,CAAC,IAAI;gEAAC;gEAAG,OAAO,QAAQ,CAAC,KAAK;;;;;;;;;;;;;8DAGtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;;gEAAK;gEAAE,OAAO,eAAe;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAKrC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;wCACV,UAAU,CAAC,mBAAmB,MAAM,SAAS;;0DAE7C,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;;oDAAC;oDAAW,QAAQ,YAAY;;;;;;;;;;;;sDAE5C,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG;;;;;;;;;;;;;;;;;8CAMjB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,OAAO,SAAS;;;;;;;;;;;;8DAIrB,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAEV,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;;gEACV,OAAO,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAiB,OAAO,IAAI,CAAC,KAAK;;;;;;;;;;;;gDAGnD,OAAO,KAAK,kBACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAiB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAOrD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAiB,OAAO,QAAQ,CAAC,OAAO;;;;;;kEACrD,8OAAC;wDAAE,WAAU;;4DACV,OAAO,QAAQ,CAAC,IAAI;4DAAC;4DAAG,OAAO,QAAQ,CAAC,KAAK;4DAAC;4DAAE,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAOhF,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAwC;4DACnD,OAAO,eAAe;;;;;;;kEAE1B,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAMnC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,MAAK;oCACL,WAAU;oCACV,UAAU,CAAC,mBAAmB,MAAM,SAAS;;sDAE7C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,CAAC,kBAAkB,kBAAkB,MAAM,SAAS,YAAY,kBAAkB;;;;;;;gCAIpF,CAAC,iCACA,8OAAC;oCAAE,WAAU;;wCAAyC;sDAC7C,8OAAC;4CAAO,SAAS,IAAM,OAAO,IAAI,CAAC;4CAAW,WAAU;sDAA0B;;;;;;wCAAc;;;;;;;gCAK1G,mBAAmB,MAAM,SAAS,2BACjC,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtE", "debugId": null}}]}