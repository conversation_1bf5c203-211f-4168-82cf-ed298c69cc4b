{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Patient.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Patient as IPatient } from '@/types';\n\nexport interface PatientDocument extends Document, Omit<IPatient, '_id'> {}\n\nconst EmergencyContactSchema = new Schema({\n  name: {\n    type: String,\n    trim: true,\n  },\n  relationship: {\n    type: String,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst MedicalHistorySchema = new Schema({\n  allergies: {\n    type: String,\n    trim: true,\n  },\n  medications: {\n    type: String,\n    trim: true,\n  },\n  conditions: {\n    type: String,\n    trim: true,\n  },\n  surgeries: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst InsuranceSchema = new Schema({\n  provider: {\n    type: String,\n    trim: true,\n  },\n  policyNumber: {\n    type: String,\n    trim: true,\n  },\n  groupNumber: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst PatientSchema = new Schema<PatientDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other', 'prefer-not-to-say'],\n  },\n  address: {\n    type: String,\n    trim: true,\n  },\n  city: {\n    type: String,\n    trim: true,\n  },\n  state: {\n    type: String,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    trim: true,\n  },\n  emergencyContact: {\n    type: EmergencyContactSchema,\n  },\n  medicalHistory: {\n    type: MedicalHistorySchema,\n  },\n  insurance: {\n    type: InsuranceSchema,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nPatientSchema.index({ firstName: 1, lastName: 1 });\nPatientSchema.index({ city: 1 });\nPatientSchema.index({ state: 1 });\n\nexport default mongoose.models.Patient || mongoose.model<PatientDocument>('Patient', PatientSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACxC,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACjC,UAAU;QACR,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAkB;IAChD,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;YAAS;SAAoB;IACxD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,cAAc,KAAK,CAAC;IAAE,WAAW;IAAG,UAAU;AAAE;AAChD,cAAc,KAAK,CAAC;IAAE,MAAM;AAAE;AAC9B,cAAc,KAAK,CAAC;IAAE,OAAO;AAAE;uCAEhB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAkB,WAAW", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface UserDocument extends Document {\n  email: string;\n  password: string;\n  role: 'doctor' | 'patient';\n  isEmailVerified: boolean;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\n\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport User from '@/models/User';\n\nexport async function authenticateUser(request: NextRequest) {\n  const authHeader = request.headers.get('Authorization');\n  const cookieToken = request.cookies.get('authToken')?.value;\n\n  const token = authHeader?.replace('Bearer ', '') || cookieToken;\n\n  if (!token) {\n    throw new Error('No authentication token provided');\n  }\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n    const user = await User.findById(decoded.userId);\n\n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Invalid or expired token');\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,eAAe,iBAAiB,OAAoB;IACzD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IAEtD,MAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;IAEpD,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAE/C,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/profile-validation.ts"], "sourcesContent": ["import { PatientDocument } from '@/models/Patient';\n\nexport interface ProfileCompleteness {\n  isComplete: boolean;\n  missingFields: string[];\n  completionPercentage: number;\n}\n\n/**\n * Check if a patient profile is complete enough for booking appointments\n * @param patient - The patient document from the database\n * @returns ProfileCompleteness object with validation results\n */\nexport function validatePatientProfileCompleteness(patient: PatientDocument | null): ProfileCompleteness {\n  if (!patient) {\n    return {\n      isComplete: false,\n      missingFields: ['Patient profile not found'],\n      completionPercentage: 0\n    };\n  }\n\n  const requiredFields = [\n    { field: 'firstName', label: 'First Name' },\n    { field: 'lastName', label: 'Last Name' },\n    { field: 'phone', label: 'Phone Number' },\n    { field: 'dateOfBirth', label: 'Date of Birth' },\n    { field: 'gender', label: 'Gender' }\n  ];\n\n  const recommendedFields = [\n    { field: 'address', label: 'Address' },\n    { field: 'city', label: 'City' },\n    { field: 'state', label: 'State' },\n    { field: 'zipCode', label: 'ZIP Code' },\n    { field: 'emergencyContact.name', label: 'Emergency Contact Name' },\n    { field: 'emergencyContact.phone', label: 'Emergency Contact Phone' }\n  ];\n\n  const missingFields: string[] = [];\n  let completedFields = 0;\n  const totalFields = requiredFields.length + recommendedFields.length;\n\n  // Check required fields\n  for (const { field, label } of requiredFields) {\n    const value = getNestedValue(patient, field);\n    if (!value || (typeof value === 'string' && value.trim() === '')) {\n      missingFields.push(label);\n    } else {\n      completedFields++;\n    }\n  }\n\n  // Check recommended fields (for completion percentage)\n  for (const { field } of recommendedFields) {\n    const value = getNestedValue(patient, field);\n    if (value && (typeof value !== 'string' || value.trim() !== '')) {\n      completedFields++;\n    }\n  }\n\n  const completionPercentage = Math.round((completedFields / totalFields) * 100);\n  const isComplete = missingFields.length === 0;\n\n  return {\n    isComplete,\n    missingFields,\n    completionPercentage\n  };\n}\n\n/**\n * Get nested value from object using dot notation\n * @param obj - The object to search in\n * @param path - The dot-separated path (e.g., 'emergencyContact.name')\n * @returns The value at the specified path\n */\nfunction getNestedValue(obj: any, path: string): any {\n  return path.split('.').reduce((current, key) => {\n    return current && current[key] !== undefined ? current[key] : null;\n  }, obj);\n}\n\n/**\n * Get user-friendly error message for incomplete profile\n * @param missingFields - Array of missing field labels\n * @returns Formatted error message\n */\nexport function getProfileIncompleteMessage(missingFields: string[]): string {\n  if (missingFields.length === 0) {\n    return '';\n  }\n\n  if (missingFields.length === 1) {\n    return `Please complete your profile by adding: ${missingFields[0]}`;\n  }\n\n  if (missingFields.length === 2) {\n    return `Please complete your profile by adding: ${missingFields.join(' and ')}`;\n  }\n\n  const lastField = missingFields.pop();\n  return `Please complete your profile by adding: ${missingFields.join(', ')}, and ${lastField}`;\n}\n"], "names": [], "mappings": ";;;;AAaO,SAAS,mCAAmC,OAA+B;IAChF,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,YAAY;YACZ,eAAe;gBAAC;aAA4B;YAC5C,sBAAsB;QACxB;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAa,OAAO;QAAa;QAC1C;YAAE,OAAO;YAAY,OAAO;QAAY;QACxC;YAAE,OAAO;YAAS,OAAO;QAAe;QACxC;YAAE,OAAO;YAAe,OAAO;QAAgB;QAC/C;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,MAAM,oBAAoB;QACxB;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAW,OAAO;QAAW;QACtC;YAAE,OAAO;YAAyB,OAAO;QAAyB;QAClE;YAAE,OAAO;YAA0B,OAAO;QAA0B;KACrE;IAED,MAAM,gBAA0B,EAAE;IAClC,IAAI,kBAAkB;IACtB,MAAM,cAAc,eAAe,MAAM,GAAG,kBAAkB,MAAM;IAEpE,wBAAwB;IACxB,KAAK,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,eAAgB;QAC7C,MAAM,QAAQ,eAAe,SAAS;QACtC,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;YAChE,cAAc,IAAI,CAAC;QACrB,OAAO;YACL;QACF;IACF;IAEA,uDAAuD;IACvD,KAAK,MAAM,EAAE,KAAK,EAAE,IAAI,kBAAmB;QACzC,MAAM,QAAQ,eAAe,SAAS;QACtC,IAAI,SAAS,CAAC,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAE,GAAG;YAC/D;QACF;IACF;IAEA,MAAM,uBAAuB,KAAK,KAAK,CAAC,AAAC,kBAAkB,cAAe;IAC1E,MAAM,aAAa,cAAc,MAAM,KAAK;IAE5C,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,eAAe,GAAQ,EAAE,IAAY;IAC5C,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS;QACtC,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,YAAY,OAAO,CAAC,IAAI,GAAG;IAChE,GAAG;AACL;AAOO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO,CAAC,wCAAwC,EAAE,aAAa,CAAC,EAAE,EAAE;IACtE;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO,CAAC,wCAAwC,EAAE,cAAc,IAAI,CAAC,UAAU;IACjF;IAEA,MAAM,YAAY,cAAc,GAAG;IACnC,OAAO,CAAC,wCAAwC,EAAE,cAAc,IAAI,CAAC,MAAM,MAAM,EAAE,WAAW;AAChG", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/patients/profile-completeness/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport Patient from '@/models/Patient';\nimport { authenticateUser } from '@/lib/auth';\nimport { validatePatientProfileCompleteness } from '@/lib/profile-validation';\nimport { ApiResponse } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if user is a patient\n    if (user.role !== 'patient') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Access denied. Only patients can access this endpoint.',\n      }, { status: 403 });\n    }\n\n    // Fetch patient profile\n    const patient = await Patient.findOne({ userId: user._id });\n    \n    // Validate profile completeness\n    const completeness = validatePatientProfileCompleteness(patient);\n\n    return NextResponse.json<ApiResponse>({\n      success: true,\n      data: completeness,\n    });\n\n  } catch (error) {\n    console.error('Profile completeness check error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to check profile completeness',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wBAAwB;QACxB,MAAM,UAAU,MAAM,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAAE,QAAQ,KAAK,GAAG;QAAC;QAEzD,gCAAgC;QAChC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,qCAAkC,AAAD,EAAE;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}