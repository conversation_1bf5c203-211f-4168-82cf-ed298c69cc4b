'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import BookingForm from '@/components/dashboard/BookingForm';
import { DoctorWithUser } from '@/types';
import { useAuth } from '@/components/providers/AuthProvider';
import { toast } from 'sonner';

interface BookingPageProps {
  params: Promise<{ doctorId: string }>;
}

export default function BookingPage({ params }: BookingPageProps) {
  const [doctor, setDoctor] = useState<DoctorWithUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();

  // Unwrap params using React.use()
  const { doctorId } = use(params);

  useEffect(() => {
    // Check if user is authenticated and is a patient
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user?.role !== 'patient') {
      toast.error('Only patients can book appointments');
      router.push('/doctors');
      return;
    }

    const fetchDoctor = async () => {
      try {
        const response = await fetch(`/api/doctors/${doctorId}`);
        const result = await response.json();

        if (result.success) {
          setDoctor(result.data);
        } else {
          toast.error('Doctor not found');
          router.push('/doctors');
        }
      } catch (error) {
        toast.error('Failed to fetch doctor information');
        router.push('/doctors');
      } finally {
        setLoading(false);
      }
    };

    fetchDoctor();
  }, [doctorId, router, isAuthenticated, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!doctor) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Doctor not found</h2>
          <Button onClick={() => router.push('/doctors')}>
            Back to Doctors
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Book Appointment</h1>
            <p className="mt-2 text-gray-600">
              Schedule your consultation with Dr. {doctor.firstName} {doctor.lastName}
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <BookingForm doctor={doctor} />
      </div>
    </div>
  );
}
