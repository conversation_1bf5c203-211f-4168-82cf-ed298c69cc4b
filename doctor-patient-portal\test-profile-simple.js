// Simple test using built-in modules
const http = require('http');
const https = require('https');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testProfileUpdate() {
  console.log('🧪 Testing Patient Profile Update...\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    // First, let's try to login with existing credentials
    console.log('1️⃣ Attempting to login...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await makeRequest(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    });

    console.log(`   Login Status: ${loginResponse.status}`);
    console.log(`   Login Response:`, loginResponse.data);

    if (loginResponse.status === 200 && loginResponse.data.success) {
      const authToken = loginResponse.data.data.token;
      console.log('✅ Login successful');
      console.log(`   Token: ${authToken.substring(0, 20)}...`);

      // Test profile update
      console.log('\n2️⃣ Testing profile update...');
      
      const updateData = {
        firstName: 'Updated',
        lastName: 'Patient',
        phone: '+1-************',
        dateOfBirth: '1990-01-15',
        gender: 'male',
        address: '123 Test Street',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        emergencyContact: {
          name: 'Emergency Contact',
          relationship: 'Spouse',
          phone: '+1-************'
        },
        medicalHistory: {
          allergies: 'None',
          medications: 'None',
          conditions: 'None',
          surgeries: 'None'
        },
        insurance: {
          provider: 'Test Insurance',
          policyNumber: 'POL123456',
          groupNumber: 'GRP789'
        }
      };

      const updateResponse = await makeRequest(`${baseUrl}/api/patients/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(updateData)
      });

      console.log(`   Update Status: ${updateResponse.status}`);
      console.log(`   Update Response:`, JSON.stringify(updateResponse.data, null, 2));

      if (updateResponse.status === 200 && updateResponse.data.success) {
        console.log('✅ Profile update successful!');
        
        // Verify the update
        console.log('\n3️⃣ Verifying update...');
        const getResponse = await makeRequest(`${baseUrl}/api/patients/me`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        console.log(`   Get Status: ${getResponse.status}`);
        if (getResponse.status === 200 && getResponse.data.success) {
          console.log('✅ Profile retrieved successfully');
          console.log(`   Name: ${getResponse.data.data.firstName} ${getResponse.data.data.lastName}`);
          console.log(`   Phone: ${getResponse.data.data.phone}`);
          console.log(`   Address: ${getResponse.data.data.address}`);
        } else {
          console.log('❌ Failed to retrieve updated profile');
          console.log('   Response:', getResponse.data);
        }
      } else {
        console.log('❌ Profile update failed');
        console.log('   Error details:', updateResponse.data);
      }
    } else {
      console.log('❌ Login failed - cannot test profile update');
      console.log('   You may need to register a user first or check credentials');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testProfileUpdate().catch(console.error);
