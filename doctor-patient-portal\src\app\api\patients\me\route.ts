import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import { authenticateUser } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    // Fetch patient profile data
    const patient = await Patient.findOne({ userId: user._id });

    if (!patient) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Patient profile not found',
      }, { status: 404 });
    }

    // Return patient profile data
    const patientData = {
      id: patient._id,
      userId: user._id,
      email: user.email,
      firstName: patient.firstName,
      lastName: patient.lastName,
      phone: patient.phone,
      dateOfBirth: patient.dateOfBirth,
      gender: patient.gender,
      address: patient.address,
      city: patient.city,
      state: patient.state,
      zipCode: patient.zipCode,
      emergencyContact: patient.emergencyContact,
      medicalHistory: patient.medicalHistory,
      insurance: patient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
    });

  } catch (error) {
    console.error('Get patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch patient profile',
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    const body = await request.json();
    console.log('📝 Patient profile update request body:', JSON.stringify(body, null, 2));

    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      city,
      state,
      zipCode,
      emergencyContact,
      medicalHistory,
      insurance
    } = body;

    // Convert dateOfBirth string to Date object if provided
    let parsedDateOfBirth = null;
    if (dateOfBirth) {
      parsedDateOfBirth = new Date(dateOfBirth);
      // Validate the date
      if (isNaN(parsedDateOfBirth.getTime())) {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: 'Invalid date format for dateOfBirth',
        }, { status: 400 });
      }
    }

    // Prepare update data, only including fields that are provided
    const updateData: any = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (parsedDateOfBirth !== null) updateData.dateOfBirth = parsedDateOfBirth;
    if (gender !== undefined) updateData.gender = gender;
    if (address !== undefined) updateData.address = address;
    if (city !== undefined) updateData.city = city;
    if (state !== undefined) updateData.state = state;
    if (zipCode !== undefined) updateData.zipCode = zipCode;
    if (emergencyContact !== undefined) updateData.emergencyContact = emergencyContact;
    if (medicalHistory !== undefined) updateData.medicalHistory = medicalHistory;
    if (insurance !== undefined) updateData.insurance = insurance;

    console.log('📝 Update data being sent to database:', JSON.stringify(updateData, null, 2));

    // Update patient profile
    const updatedPatient = await Patient.findOneAndUpdate(
      { userId: user._id },
      { $set: updateData },
      { new: true, runValidators: true, upsert: true }
    );

    if (!updatedPatient) {
      console.error('❌ Failed to update patient profile - updatedPatient is null');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to update patient profile',
      }, { status: 500 });
    }

    console.log('✅ Patient profile updated successfully:', updatedPatient._id);

    const patientData = {
      id: updatedPatient._id,
      userId: user._id,
      email: user.email,
      firstName: updatedPatient.firstName,
      lastName: updatedPatient.lastName,
      phone: updatedPatient.phone,
      dateOfBirth: updatedPatient.dateOfBirth,
      gender: updatedPatient.gender,
      address: updatedPatient.address,
      city: updatedPatient.city,
      state: updatedPatient.state,
      zipCode: updatedPatient.zipCode,
      emergencyContact: updatedPatient.emergencyContact,
      medicalHistory: updatedPatient.medicalHistory,
      insurance: updatedPatient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
      message: 'Patient profile updated successfully',
    });

  } catch (error) {
    console.error('Update patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update patient profile',
    }, { status: 500 });
  }
}
