import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import { authenticateUser } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    // Fetch patient profile data
    const patient = await Patient.findOne({ userId: user._id });

    if (!patient) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Patient profile not found',
      }, { status: 404 });
    }

    // Return patient profile data
    const patientData = {
      id: patient._id,
      userId: user._id,
      email: user.email,
      firstName: patient.firstName,
      lastName: patient.lastName,
      phone: patient.phone,
      dateOfBirth: patient.dateOfBirth,
      gender: patient.gender,
      address: patient.address,
      city: patient.city,
      state: patient.state,
      zipCode: patient.zipCode,
      emergencyContact: patient.emergencyContact,
      medicalHistory: patient.medicalHistory,
      insurance: patient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
    });

  } catch (error) {
    console.error('Get patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch patient profile',
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    const body = await request.json();
    console.log('📝 Patient profile update request body:', JSON.stringify(body, null, 2));

    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      city,
      state,
      zipCode,
      emergencyContact,
      medicalHistory,
      insurance
    } = body;

    // Convert dateOfBirth string to Date object if provided
    let parsedDateOfBirth = null;
    if (dateOfBirth) {
      parsedDateOfBirth = new Date(dateOfBirth);
      // Validate the date
      if (isNaN(parsedDateOfBirth.getTime())) {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: 'Invalid date format for dateOfBirth',
        }, { status: 400 });
      }
    }

    // Prepare update data, only including fields that are provided and not empty
    const updateData: any = {};
    if (firstName !== undefined && firstName.trim() !== '') updateData.firstName = firstName.trim();
    if (lastName !== undefined && lastName.trim() !== '') updateData.lastName = lastName.trim();
    if (phone !== undefined && phone.trim() !== '') updateData.phone = phone.trim();
    if (parsedDateOfBirth !== null) updateData.dateOfBirth = parsedDateOfBirth;
    if (gender !== undefined && gender.trim() !== '') updateData.gender = gender.trim();
    if (address !== undefined && address.trim() !== '') updateData.address = address.trim();
    if (city !== undefined && city.trim() !== '') updateData.city = city.trim();
    if (state !== undefined && state.trim() !== '') updateData.state = state.trim();
    if (zipCode !== undefined && zipCode.trim() !== '') updateData.zipCode = zipCode.trim();
    // Handle nested objects - only include if they have meaningful data
    if (emergencyContact !== undefined) {
      const cleanedEmergencyContact = {
        name: emergencyContact.name?.trim() || '',
        relationship: emergencyContact.relationship?.trim() || '',
        phone: emergencyContact.phone?.trim() || ''
      };
      updateData.emergencyContact = cleanedEmergencyContact;
    }

    if (medicalHistory !== undefined) {
      const cleanedMedicalHistory = {
        allergies: medicalHistory.allergies?.trim() || '',
        medications: medicalHistory.medications?.trim() || '',
        conditions: medicalHistory.conditions?.trim() || '',
        surgeries: medicalHistory.surgeries?.trim() || ''
      };
      updateData.medicalHistory = cleanedMedicalHistory;
    }

    if (insurance !== undefined) {
      const cleanedInsurance = {
        provider: insurance.provider?.trim() || '',
        policyNumber: insurance.policyNumber?.trim() || '',
        groupNumber: insurance.groupNumber?.trim() || ''
      };
      updateData.insurance = cleanedInsurance;
    }

    console.log('📝 Update data being sent to database:', JSON.stringify(updateData, null, 2));

    // Update patient profile
    const updatedPatient = await Patient.findOneAndUpdate(
      { userId: user._id },
      { $set: updateData },
      { new: true, runValidators: true, upsert: true }
    );

    if (!updatedPatient) {
      console.error('❌ Failed to update patient profile - updatedPatient is null');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to update patient profile',
      }, { status: 500 });
    }

    console.log('✅ Patient profile updated successfully:', updatedPatient._id);

    const patientData = {
      id: updatedPatient._id,
      userId: user._id,
      email: user.email,
      firstName: updatedPatient.firstName,
      lastName: updatedPatient.lastName,
      phone: updatedPatient.phone,
      dateOfBirth: updatedPatient.dateOfBirth,
      gender: updatedPatient.gender,
      address: updatedPatient.address,
      city: updatedPatient.city,
      state: updatedPatient.state,
      zipCode: updatedPatient.zipCode,
      emergencyContact: updatedPatient.emergencyContact,
      medicalHistory: updatedPatient.medicalHistory,
      insurance: updatedPatient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
      message: 'Patient profile updated successfully',
    });

  } catch (error) {
    console.error('Update patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update patient profile',
    }, { status: 500 });
  }
}
